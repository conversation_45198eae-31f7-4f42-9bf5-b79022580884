<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile>SimpleProxy.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="11f55c9c-6d46-4c44-8ab7-6474c6ed755d" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/../../gui/proxy-tool/src/main/kotlin/net/rsprox/gui/sessions/SessionPanel.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../../gui/proxy-tool/src/main/kotlin/net/rsprox/gui/sessions/SessionPanel.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../gui/proxy-tool/src/main/kotlin/net/rsprox/gui/sessions/SessionType.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../../gui/proxy-tool/src/main/kotlin/net/rsprox/gui/sessions/SessionType.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../patch/patch-native/src/main/kotlin/net/rsprox/patch/native/NativePatchCriteria.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../../patch/patch-native/src/main/kotlin/net/rsprox/patch/native/NativePatchCriteria.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../patch/src/main/kotlin/net/rsprox/patch/NativeClientType.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../../patch/src/main/kotlin/net/rsprox/patch/NativeClientType.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../proxy/src/main/kotlin/net/rsprox/proxy/ProxyService.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../../proxy/src/main/kotlin/net/rsprox/proxy/ProxyService.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../proxy/src/main/kotlin/net/rsprox/proxy/client/ClientLoginInitializer.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../../proxy/src/main/kotlin/net/rsprox/proxy/client/ClientLoginInitializer.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../proxy/src/main/kotlin/net/rsprox/proxy/config/RSProx.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../../proxy/src/main/kotlin/net/rsprox/proxy/config/RSProx.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../proxy/src/main/kotlin/net/rsprox/proxy/downloader/JagexNativeClientDownloader.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../../proxy/src/main/kotlin/net/rsprox/proxy/downloader/JagexNativeClientDownloader.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../proxy/src/main/kotlin/net/rsprox/proxy/downloader/LostCityNativeClientDownloader.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../../proxy/src/main/kotlin/net/rsprox/proxy/downloader/LostCityNativeClientDownloader.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../proxy/src/main/kotlin/net/rsprox/proxy/server/ServerGenericDecoder.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../../proxy/src/main/kotlin/net/rsprox/proxy/server/ServerGenericDecoder.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../proxy/src/main/kotlin/net/rsprox/proxy/util/ClientType.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../../proxy/src/main/kotlin/net/rsprox/proxy/util/ClientType.kt" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/../.." />
  </component>
  <component name="GitHubPullRequestSearchHistory"><![CDATA[{
  "lastFilter": {
    "state": "OPEN",
    "assignee": "kret126"
  }
}]]></component>
  <component name="GithubPullRequestsUISettings"><![CDATA[{
  "selectedUrlAndAccountId": {
    "url": "https://github.com/blurite/rsprox.git",
    "accountId": "7cc55065-dffc-472c-918e-ed92ae5ff2ec"
  }
}]]></component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/f4def76301a6443195b7ec591de0bd3c85910/ec/5949f073/TcpClient.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/783d1dc48cd9dda2ce32a4b7f3b54c565cfc7914ab1dc5af92d95ec8f166353b/MemoryExtensions.cs" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="KubernetesApiPersistence"><![CDATA[{}]]></component>
  <component name="KubernetesApiProvider"><![CDATA[{
  "isMigrated": true
}]]></component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 4
}]]></component>
  <component name="ProjectId" id="32sptD6rrhgQN2Ho2ooczuFw7AV" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    ".NET Project.SimpleProxy.executor": "Debug",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="SimpleProxy" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/SimpleProxy.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="11f55c9c-6d46-4c44-8ab7-6474c6ed755d" name="Changes" comment="" />
      <created>1758219346323</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1758219346323</updated>
      <workItem from="1758219347458" duration="10700000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>