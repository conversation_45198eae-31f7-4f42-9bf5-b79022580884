using System.Net.Sockets;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.Math;
using SimpleProxy;

namespace SimpleProxy.Interceptors.PreLogin;

public static class PreLogin
{
    public static async Task<Session?> RunAsync(NetworkStream client, NetworkStream upstream, RsaPrivateCrtKeyParameters privKey)
    {
        while (true)
        {
            int op = await SimpleProxy.NetIO.ReadOneAsync(client);
            if (op == -1)
            {
                Console.WriteLine("[PreLogin] Client closed before login");
                return null;
            }
            byte opcode = (byte)op;

            switch (opcode)
            {
                case 14: // INIT_GAME_CONNECTION (size 0)
                case 27: // SSL_WEB_CONNECTION (size 0)
                    await upstream.WriteAsync(new[] { opcode });
                    await upstream.FlushAsync();
                    break;
                case 15: // INIT_JS5REMOTE_CONNECTION (size 20)
                    await SimpleProxy.NetIO.ForwardFixedAsync(client, upstream, opcode, 20);
                    break;
                case 19: // POW_REPLY (var-short)
                    await SimpleProxy.NetIO.ForwardVarShortAsync(client, upstream, opcode);
                    break;
                case 20: // REMAINING_BETA_ARCHIVE_CRCS_V1 (58)
                    await SimpleProxy.NetIO.ForwardFixedAsync(client, upstream, opcode, 58);
                    break;
                case 32: // REMAINING_BETA_ARCHIVE_CRCS_V2 (66)
                    await SimpleProxy.NetIO.ForwardFixedAsync(client, upstream, opcode, 66);
                    break;
                case 21: // UNKNOWN (37)
                    await SimpleProxy.NetIO.ForwardFixedAsync(client, upstream, opcode, 37);
                    break;

                case 16: // GAMELOGIN
                case 18: // GAMERECONNECT
                    {
                        var session = await HandleLoginAsync(opcode, client, upstream, privKey);
                        Console.WriteLine("[PreLogin] Completed pre-login phase");
                        return session;
                    }

                default:
                    // Unknown during prelogin: best-effort passthrough as single byte then end prelogin
                    await upstream.WriteAsync(new[] { opcode });
                    await upstream.FlushAsync();
                    return null;
            }
        }
    }

    private static async Task<Session> HandleLoginAsync(byte opcode, NetworkStream client, NetworkStream upstream, RsaPrivateCrtKeyParameters privKey)
    {
        var (payloadLen, payload) = await SimpleProxy.NetIO.ReadVarShortPayloadAsync(client);
        if (payloadLen < 0) return new Session(new uint[] { 0, 0, 0, 0 }); // closed; dummy
        Console.WriteLine($"[Login] op=0x{opcode:X2} len={payloadLen}");

        if (string.IsNullOrWhiteSpace(Config.OriginalServerModulusHex))
            throw new InvalidOperationException("Original server modulus is not configured (Config.OriginalServerModulusHex)");
        var serverMod = new BigInteger(Config.OriginalServerModulusHex, 16);

        if (!FindRsaBlock(payload, payloadLen, privKey, out int rsaOffset, out int rsaLen, out byte[] decrypted))
            throw new InvalidOperationException("Could not locate RSA block in login payload.");

        Console.WriteLine($"[Login] Found RSA block at off={rsaOffset} len={rsaLen}");
        Console.WriteLine($"[Login] RSA seeds/log: {FormatSeeds(decrypted)}");

        // Extract encodeSeed (four big-endian ints) from decrypted RSA block (after leading 0x01)
        var encodeSeed = new uint[4];
        int seedOff = 1;
        for (int i = 0; i < 4; i++)
        {
            encodeSeed[i] = (uint)((decrypted[seedOff] << 24) | (decrypted[seedOff + 1] << 16) | (decrypted[seedOff + 2] << 8) | decrypted[seedOff + 3]);
            seedOff += 4;
        }

        // Decrypt and format login XTEA block for logging
        int xteaStart = rsaOffset + 2 + rsaLen;
        int xteaLen = Math.Max(0, payloadLen - xteaStart);
        if (xteaLen > 0)
        {
            var xteaCipher = new byte[xteaLen];
            Buffer.BlockCopy(payload, xteaStart, xteaCipher, 0, xteaLen);
            var xteaPlain = XteaDecrypt(xteaCipher, decrypted);
            var info = FormatLoginData(xteaPlain);
            Console.WriteLine($"Original login xtea block: {info}");
        }

        var encrypted = RsaHelper.EncryptRaw(decrypted, serverMod);
        Console.WriteLine($"[Login] Rewrote RSA: oldLen={rsaLen} newLen={encrypted.Length}");

        await WriteLoginWithReplacedRsaAsync(opcode, payload, payloadLen, rsaOffset, rsaLen, encrypted, upstream);
        return new Session(encodeSeed);
    }



    private static bool FindRsaBlock(byte[] payload, int payloadLen, RsaPrivateCrtKeyParameters priv,
        out int rsaOffset, out int rsaLen, out byte[] decrypted)
    {
        rsaOffset = -1; rsaLen = -1; decrypted = Array.Empty<byte>();
        for (int i = 0; i + 2 <= payloadLen; i++)
        {
            int len = (payload[i] << 8) | payload[i + 1];
            if (len <= 0 || len > payloadLen) continue;
            int after = i + 2;
            if (after + len > payloadLen) continue;
            var candidate = new byte[len];
            Buffer.BlockCopy(payload, after, candidate, 0, len);
            var plain = RsaHelper.DecryptRaw(candidate, priv);
            if (plain.Length > 0 && plain[0] == 0x01)
            {
                rsaOffset = i;
                rsaLen = len;
                decrypted = plain;
                return true;
            }
        }
        return false;
    }

    private static byte[] XteaDecrypt(byte[] cipher, byte[] rsaPlain)
    {
        if (cipher.Length < 8 || rsaPlain.Length < 17) return Array.Empty<byte>();
        uint[] k = new uint[4];
        int seedOff = 1;
        for (int i = 0; i < 4; i++)
        {
            k[i] = (uint)((rsaPlain[seedOff] << 24) | (rsaPlain[seedOff + 1] << 16) | (rsaPlain[seedOff + 2] << 8) | rsaPlain[seedOff + 3]);
            seedOff += 4;
        }
        var data = new byte[cipher.Length - (cipher.Length % 8)];
        Buffer.BlockCopy(cipher, 0, data, 0, data.Length);
        const uint delta = 0x9E3779B9u;
        for (int off = 0; off + 8 <= data.Length; off += 8)
        {
            uint v0 = (uint)((data[off] << 24) | (data[off + 1] << 16) | (data[off + 2] << 8) | data[off + 3]);
            uint v1 = (uint)((data[off + 4] << 24) | (data[off + 5] << 16) | (data[off + 6] << 8) | data[off + 7]);
            uint sum = 0xC6EF3720u;
            for (int i = 0; i < 32; i++)
            {
                v1 -= (((v0 << 4) ^ (v0 >> 5)) + v0) ^ (sum + k[(sum >> 11) & 3]);
                sum -= delta;
                v0 -= (((v1 << 4) ^ (v1 >> 5)) + v1) ^ (sum + k[sum & 3]);
            }
            data[off] = (byte)(v0 >> 24);
            data[off + 1] = (byte)(v0 >> 16);
            data[off + 2] = (byte)(v0 >> 8);
            data[off + 3] = (byte)v0;
            data[off + 4] = (byte)(v1 >> 24);
            data[off + 5] = (byte)(v1 >> 16);
            data[off + 6] = (byte)(v1 >> 8);
            data[off + 7] = (byte)v1;
        }
        return data;
    }

    private static string ReadJstr(byte[] buf, ref int off)
    {
        int start = off;
        while (off < buf.Length && buf[off] != 0) off++;
        var s = System.Text.Encoding.ASCII.GetString(buf, start, Math.Max(0, off - start));
        if (off < buf.Length && buf[off] == 0) off++;
        return s;
    }
    private static string ReadJstr2(byte[] buf, ref int off) => ReadJstr(buf, ref off);

    private static int U1(byte[] b, ref int o) => (o < b.Length) ? b[o++] & 0xFF : 0;
    private static int U2(byte[] b, ref int o)
    {
        if (o + 2 > b.Length) { o = b.Length; return 0; }
        int v = (b[o] << 8) | b[o + 1];
        o += 2;
        return v & 0xFFFF;
    }
    private static int U3(byte[] b, ref int o)
    {
        if (o + 3 > b.Length) { o = b.Length; return 0; }
        int v = (b[o] << 16) | (b[o + 1] << 8) | b[o + 2];
        o += 3;
        return v & 0xFFFFFF;
    }
    private static int U4(byte[] b, ref int o)
    {
        if (o + 4 > b.Length) { o = b.Length; return 0; }
        int v = (b[o] << 24) | (b[o + 1] << 16) | (b[o + 2] << 8) | b[o + 3];
        o += 4;
        return v;
    }

    private static string FormatHostPlatformStats(byte[] b, ref int off)
    {
        int version = U1(b, ref off);
        int osType = U1(b, ref off);
        int os64Bit = U1(b, ref off);
        int osVersion = U2(b, ref off);
        int javaVendor = U1(b, ref off);
        int javaVersionMajor = U1(b, ref off);
        int javaVersionMinor = U1(b, ref off);
        int javaVersionPatch = U1(b, ref off);
        int applet = U1(b, ref off);
        int javaMaxMemoryMb = U2(b, ref off);
        int javaAvailableProcessors = U1(b, ref off);
        int systemMemory = U3(b, ref off);
        int systemSpeed = U2(b, ref off);
        string gpuDxName = ReadJstr2(b, ref off);
        string gpuGlName = ReadJstr2(b, ref off);
        string gpuDxVersion = ReadJstr2(b, ref off);
        string gpuGlVersion = ReadJstr2(b, ref off);
        int gpuDriverMonth = U1(b, ref off);
        int gpuDriverYear = U2(b, ref off);
        string cpuManufacturer = ReadJstr2(b, ref off);
        string cpuBrand = ReadJstr2(b, ref off);
        int cpuCount1 = U1(b, ref off);
        int cpuCount2 = U1(b, ref off);
        int[] cpuFeatures = new int[3];
        for (int i = 0; i < 3; i++) cpuFeatures[i] = U4(b, ref off);
        int cpuSignature = U4(b, ref off);
        string clientName = ReadJstr2(b, ref off);
        string deviceName = ReadJstr2(b, ref off);
        return $"HostPlatformStats(version={version}, osType={osType}, os64Bit={os64Bit}, osVersion={osVersion}, javaVendor={javaVendor}, javaVersionMajor={javaVersionMajor}, javaVersionMinor={javaVersionMinor}, javaVersionPatch={javaVersionPatch}, applet={applet}, javaMaxMemoryMb={javaMaxMemoryMb}, javaAvailableProcessors={javaAvailableProcessors}, systemMemory={systemMemory}, systemSpeed={systemSpeed}, gpuDxName={gpuDxName}, gpuGlName={gpuGlName}, gpuDxVersion={gpuDxVersion}, gpuGlVersion={gpuGlVersion}, gpuDriverMonth={gpuDriverMonth}, gpuDriverYear={gpuDriverYear}, cpuManufacturer={cpuManufacturer}, cpuBrand={cpuBrand}, cpuCount1={cpuCount1}, cpuCount2={cpuCount2}, cpuFeatures=[{string.Join(",", cpuFeatures)}], cpuSignature={cpuSignature}, clientName={clientName}, deviceName={deviceName})";
    }

    private static string FormatLoginData(byte[] xteaPlain)
    {
        if (xteaPlain.Length == 0) return "<no xtea data>";
        int off = 0;
        string username = ReadJstr(xteaPlain, ref off);
        int packedClientSettings = U1(xteaPlain, ref off);
        int width = U2(xteaPlain, ref off);
        int height = U2(xteaPlain, ref off);
        var uuid = new byte[24];
        for (int i = 0; i < 24 && off < xteaPlain.Length; i++) uuid[i] = (byte)U1(xteaPlain, ref off);
        string siteSettings = ReadJstr(xteaPlain, ref off);
        int affiliate = U4(xteaPlain, ref off);
        int deepLinkCount = U1(xteaPlain, ref off);
        var links = new List<int>();
        for (int i = 0; i < deepLinkCount; i++) links.Add(U4(xteaPlain, ref off));
        string hostStats = FormatHostPlatformStats(xteaPlain, ref off);
        int secondClientType = U1(xteaPlain, ref off);
        int reflectionCheckerConst = U4(xteaPlain, ref off);
        int crcLen = Math.Max(0, xteaPlain.Length - off);
        return $"LoginXteaBlock(username={username}, packedClientSettings={packedClientSettings}, width={width}, height={height}, uuid={BitConverter.ToString(uuid).Replace("-", "")}, siteSettings={siteSettings}, affiliate={affiliate}, deepLinks=[{string.Join(",", links)}], hostPlatformStats={hostStats}, secondClientType={secondClientType}, reflectionCheckerConst={reflectionCheckerConst}, crcLen={crcLen})";
    }


    private static string FormatSeeds(byte[] decrypted)
    {
        if (decrypted.Length < 17) return "<insufficient>";
        int off = 1;
        static uint U32BE(byte[] b, int o) => (uint)((b[o] << 24) | (b[o + 1] << 16) | (b[o + 2] << 8) | b[o + 3]);
        uint s0 = U32BE(decrypted, off);
        uint s1 = U32BE(decrypted, off + 4);
        uint s2 = U32BE(decrypted, off + 8);
        uint s3 = U32BE(decrypted, off + 12);
        return $"seed={s0:X8},{s1:X8},{s2:X8},{s3:X8}";
    }

    private static async Task WriteLoginWithReplacedRsaAsync(
        byte opcode,
        byte[] payload,
        int payloadLen,
        int rsaOffset,
        int rsaLen,
        byte[] newCipher,
        NetworkStream upstream)
    {
        var newPayloadLen = payloadLen - (2 + rsaLen) + (2 + newCipher.Length);
        var head = new byte[3] { opcode, (byte)(newPayloadLen >> 8), (byte)(newPayloadLen & 0xFF) };
        await upstream.WriteAsync(head, 0, 3);

        if (rsaOffset > 0)
            await upstream.WriteAsync(payload.AsMemory(0, rsaOffset));

        var lenBytes = new byte[2] { (byte)(newCipher.Length >> 8), (byte)(newCipher.Length & 0xFF) };
        await upstream.WriteAsync(lenBytes, 0, 2);
        await upstream.WriteAsync(newCipher, 0, newCipher.Length);

        int oldRsaBlockEnd = rsaOffset + 2 + rsaLen;
        int tail = payloadLen - oldRsaBlockEnd;
        if (tail > 0)
            await upstream.WriteAsync(payload.AsMemory(oldRsaBlockEnd, tail));

        await upstream.FlushAsync();
    }
}

