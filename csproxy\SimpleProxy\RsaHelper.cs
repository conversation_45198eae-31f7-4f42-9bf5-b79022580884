using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Generators;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.Math;
using Org.BouncyCastle.OpenSsl;
using Org.BouncyCastle.Security;

namespace SimpleProxy;

public static class RsaHelper
{
    public static AsymmetricCipherKeyPair LoadOrCreateDefaultKey()
    {
        var path = Config.KeyPath;
        Directory.CreateDirectory(Path.GetDirectoryName(path)!);
        if (File.Exists(path))
        {
            using var rd = File.OpenText(path);
            var pemReader = new PemReader(rd);
            var obj = pemReader.ReadObject();
            switch (obj)
            {
                case AsymmetricCipherKeyPair kp:
                    Console.WriteLine($"[SimpleProxy] RSA key loaded from {path} (keypair)");
                    return kp;
                case AsymmetricKeyParameter akp when akp.IsPrivate:
                    {
                        // PKCS#8 "PRIVATE KEY" path (what RSProx writes)
                        if (akp is RsaPrivateCrtKeyParameters privCrt)
                        {
                            var pub = new RsaKeyParameters(false, privCrt.Modulus, privCrt.PublicExponent);
                            Console.WriteLine($"[SimpleProxy] RSA private key loaded from {path} (PKCS#8)");
                            return new AsymmetricCipherKeyPair(pub, privCrt);
                        }
                        throw new InvalidOperationException("Unsupported private key algorithm in PEM");
                    }
                case RsaPrivateCrtKeyParameters priv:
                    {
                        var pub = new RsaKeyParameters(false, priv.Modulus, priv.PublicExponent);
                        Console.WriteLine($"[SimpleProxy] RSA private key loaded from {path} (CRT)");
                        return new AsymmetricCipherKeyPair(pub, priv);
                    }
                default:
                    throw new InvalidOperationException("Unsupported PEM content in key.rsa");
            }
        }
        else
        {
            var kp = GenerateKeyPair(1008); // OSRS client limit
            using var wr = File.CreateText(path);
            var pemWriter = new PemWriter(wr);
            pemWriter.WriteObject(kp.Private); // writes a usable PEM; RSProx can still read
            wr.Flush();
            Console.WriteLine($"[SimpleProxy] RSA key generated to {path}");
            return kp;
        }
    }

    public static AsymmetricCipherKeyPair GenerateKeyPair(int bits)
    {
        var gen = new RsaKeyPairGenerator();
        var parms = new RsaKeyGenerationParameters(new BigInteger(Config.PublicExponent.ToString()), new SecureRandom(), bits, 80);
        gen.Init(parms);
        return gen.GenerateKeyPair();
    }

    public static string GetPublicModulusHex(AsymmetricCipherKeyPair kp)
    {
        var pub = (RsaKeyParameters)kp.Public;
        return pub.Modulus.ToString(16).ToUpperInvariant();
    }

    public static byte[] DecryptRaw(ReadOnlySpan<byte> ciphertext, RsaPrivateCrtKeyParameters priv)
    {
        // Raw RSA: m = c^d mod n (CRT used by Bouncy for speed if desired)
        var c = new BigInteger(1, ciphertext.ToArray());
        var m = c.ModPow(priv.Exponent, priv.Modulus); // using D directly
        // Return minimal big-endian like Kotlin path does (no leading zero padding)
        return m.ToByteArrayUnsigned();
    }

    public static byte[] EncryptRaw(ReadOnlySpan<byte> plaintext, BigInteger modulus)
    {
        var e = new BigInteger(Config.PublicExponent.ToString());
        var m = new BigInteger(1, plaintext.ToArray());
        var c = m.ModPow(e, modulus);
        // Return fixed-length big-endian equal to modulus byte length (to match Kotlin/RS client expectations)
        var modBytes = ByteLength(modulus);
        return ToFixedLength(c, modBytes);
    }

    public static int ByteLength(BigInteger n)
    {
        // ceil(bitlen/8)
        return (n.BitLength + 7) / 8;
    }

    public static byte[] ToFixedLength(BigInteger n, int len)
    {
        var raw = n.ToByteArrayUnsigned();
        if (raw.Length == len) return raw;
        if (raw.Length > len)
        {
            var start = raw.Length - len;
            var tmp = new byte[len];
            Buffer.BlockCopy(raw, start, tmp, 0, len);
            return tmp;
        }
        else
        {
            var tmp = new byte[len];
            Buffer.BlockCopy(raw, 0, tmp, len - raw.Length, raw.Length);
            return tmp;
        }
    }
}

