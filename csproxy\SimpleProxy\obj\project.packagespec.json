﻿"restore":{"projectUniqueName":"C:\\Users\\<USER>\\Desktop\\rsprox\\csproxy\\SimpleProxy\\SimpleProxy.csproj","projectName":"SimpleProxy","projectPath":"C:\\Users\\<USER>\\Desktop\\rsprox\\csproxy\\SimpleProxy\\SimpleProxy.csproj","outputPath":"C:\\Users\\<USER>\\Desktop\\rsprox\\csproxy\\SimpleProxy\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net8.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"C:\\Program Files\\dotnet\\library-packs":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net8.0":{"targetAlias":"net8.0","projectReferences":{}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.200"}"frameworks":{"net8.0":{"targetAlias":"net8.0","dependencies":{"BouncyCastle.Cryptography":{"target":"Package","version":"[2.6.2, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"downloadDependencies":[{"name":"Microsoft.AspNetCore.App.Ref","version":"[8.0.14, 8.0.14]"},{"name":"Microsoft.NETCore.App.Host.win-x64","version":"[8.0.14, 8.0.14]"},{"name":"Microsoft.NETCore.App.Ref","version":"[8.0.14, 8.0.14]"},{"name":"Microsoft.WindowsDesktop.App.Ref","version":"[8.0.14, 8.0.14]"}],"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.202/PortableRuntimeIdentifierGraph.json"}}