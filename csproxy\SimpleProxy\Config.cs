namespace SimpleProxy;

public static class Config
{
    // Listener
    public const string ListenHost = "127.0.0.1";
    public const int ListenPort = 12500; // as requested

    // Upstream world (hardcoded)
    public const string UpstreamHost = "oldschool8a.runescape.com";
    public const int UpstreamPort = 43594; // agreed

    // RSA exponent used by OSRS
    public const int PublicExponent = 0x10001; // 65537

    // IMPORTANT: paste the original server RSA modulus (hex, without 0x) here.
    // Example format: "D4A1...". Leave empty to log a warning and fail login rewriting.
    public const string OriginalServerModulusHex = "9957911959d8104656e68732f648a08538ca7861515bee940af7469f31f3e5526948ae1fe7cf6ba41a75dcd03954749b031fae609e4e1cec31f9efeb77d333c674efb10797d29595b1e30c59b637c7913bb1184fc5737fffa1f476b90397cd2fc3a76296401d92306ec71d6bf2980b2b84bc3cf055ad4a56e08d3f426e4c0faf";

    // Private key storage location (PEM) — match RSProx: %USERPROFILE%/.rsprox/key.rsa
    public static readonly string KeyPath =
        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), ".rsprox", "key.rsa");
}

