package net.rsprox.proxy.server

import io.netty.buffer.ByteBuf
import io.netty.channel.ChannelHandlerContext
import io.netty.handler.codec.ByteToMessageDecoder
import com.github.michaelbull.logging.InlineLogger
import net.rsprot.buffer.extensions.g1
import net.rsprot.buffer.extensions.g2
import net.rsprot.crypto.cipher.StreamCipher
import net.rsprot.protocol.Prot
import net.rsprox.protocol.ProtProvider

public class ServerGenericDecoder<out T : Prot>(
    private val cipher: StreamCipher,
    private val protProvider: ProtProvider<T>,
) : ByteToMessageDecoder() {
    private enum class State {
        READ_OPCODE_P1,
        READ_OPCODE_P2,
        READ_LENGTH,
        READ_PAYLOAD,
    }

    private companion object {
        private val logger = InlineLogger()
    }

    private var state: State = State.READ_OPCODE_P1
    private var cipherMod1: Int = -1
    private var cipherMod2: Int = -1
    private var opcode: Int = -1
    private var length: Int = 0
    private lateinit var prot: T

    override fun decode(
        ctx: ChannelHandlerContext,
        input: ByteBuf,
        out: MutableList<Any>,
    ) {
        if (state == State.READ_OPCODE_P1) {
            if (!input.isReadable) {
                return
            }
            this.cipherMod1 = cipher.nextInt()
            this.cipherMod2 = 0
            this.opcode = (input.g1() - cipherMod1) and 0xFF
            logger.debug { "SrvDec READ_OPCODE_P1: cipherMod1=$cipherMod1, p1Dec=$opcode" }
            if (this.opcode >= 128) {
                logger.debug { "SrvDec two-byte opcode expected, p1=$opcode" }
                state = State.READ_OPCODE_P2
            } else {
                try {
                    this.prot = protProvider[opcode]
                    this.length = this.prot.size
                    logger.debug { "SrvDec mapped opcode=$opcode prot=$prot size=$length" }
                } catch (e: IllegalArgumentException) {
                    logger.error(e) { "SrvDec unknown server prot (1-byte) opcode=$opcode, cipherMod1=$cipherMod1" }
                    throw e
                }
                state =
                    if (this.length >= 0) {
                        State.READ_PAYLOAD
                    } else {
                        State.READ_LENGTH
                    }
            }
        }

        if (state == State.READ_OPCODE_P2) {
            if (!input.isReadable) {
                return
            }
            val opcodeP1 = this.opcode
            this.cipherMod2 = cipher.nextInt()
            val b2Dec = (input.g1() - cipherMod2) and 0xFF
            this.opcode = ((opcodeP1 - 128) shl 8) + b2Dec
            logger.debug { "SrvDec READ_OPCODE_P2: p1=$opcodeP1, cipherMod2=$cipherMod2, p2Dec=$b2Dec => opcode=$opcode" }
            try {
                this.prot = protProvider[opcode]
                this.length = this.prot.size
                logger.debug { "SrvDec mapped opcode=$opcode prot=$prot size=$length" }
            } catch (e: IllegalArgumentException) {
                logger.error(e) { "SrvDec unknown server prot (2-byte) opcode=$opcode (p1=$opcodeP1, p2Dec=$b2Dec, cipherMods=[$cipherMod1,$cipherMod2])" }
                throw e
            }
            state =
                if (this.length >= 0) {
                    State.READ_PAYLOAD
                } else {
                    State.READ_LENGTH
                }
        }

        if (state == State.READ_LENGTH) {
            when (length) {
                Prot.VAR_BYTE -> {
                    if (!input.isReadable(Byte.SIZE_BYTES)) {
                        return
                    }
                    this.length = input.g1()
                }

                Prot.VAR_SHORT -> {
                    if (!input.isReadable(Short.SIZE_BYTES)) {
                        return
                    }
                    this.length = input.g2()
                }

                else -> {
                    throw IllegalStateException("Invalid length: $length")
                }
            }
            state = State.READ_PAYLOAD
        }

        if (state == State.READ_PAYLOAD) {
            if (!input.isReadable(length)) {
                return
            }
            val payload = input.readBytes(length)
            out +=
                ServerPacket(
                    this.prot,
                    this.cipherMod1,
                    this.cipherMod2,
                    payload,
                )
            state = State.READ_OPCODE_P1
        }
    }
}
