namespace SimpleProxy;

public sealed class Session
{
    // ISAAC for client->server (decode): seed = encodeSeed + 50
    public Isaac InboundIsaac { get; }
    // ISAAC for server->client (encode): seed = encodeSeed
    public Isaac OutboundIsaac { get; }

    public Session(uint[] encodeSeed)
    {
        var dec = new uint[encodeSeed.Length];
        for (int i = 0; i < encodeSeed.Length; i++) dec[i] = encodeSeed[i] + 50u;
        InboundIsaac = new <PERSON>(dec);
        OutboundIsaac = new <PERSON>(encodeSeed);
    }
}

