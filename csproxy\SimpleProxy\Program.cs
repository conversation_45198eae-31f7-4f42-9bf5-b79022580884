﻿using SimpleProxy;

Console.Title = "SimpleProxy";

try
{
    Console.WriteLine("[SimpleProxy] Starting...");

    // Load or generate our 1008-bit RSA keypair used by the patched client
    var key = RsaHelper.LoadOrCreateDefaultKey();
    Console.WriteLine("[SimpleProxy] Proxy RSA public modulus (hex):");
    Console.WriteLine(RsaHelper.GetPublicModulusHex(key));

    // Also log the server modulus configured in Config
    Console.WriteLine("[SimpleProxy] Server RSA modulus (hex) from Config.OriginalServerModulusHex:");
    Console.WriteLine(string.IsNullOrWhiteSpace(Config.OriginalServerModulusHex) ? "<NOT SET>" : Config.OriginalServerModulusHex.ToUpperInvariant());

    if (string.IsNullOrWhiteSpace(Config.OriginalServerModulusHex))
    {
        Console.ForegroundColor = ConsoleColor.Yellow;
        Console.WriteLine("[SimpleProxy] WARNING: Config.OriginalServerModulusHex is empty. Login rewriting will fail.");
        Console.ResetColor();
    }

    var listener = new ProxyListener(Config.ListenHost, Config.ListenPort, Config.UpstreamHost, Config.UpstreamPort, key);
    await listener.RunAsync();
}
catch (Exception ex)
{
    Console.ForegroundColor = ConsoleColor.Red;
    Console.WriteLine("[SimpleProxy] Fatal error: " + ex);
    Console.ResetColor();
}
