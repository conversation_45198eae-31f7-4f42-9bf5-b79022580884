[versions]
kotlin = "2.2.10"
netty = "4.2.2.Final"
rsprot = "1.0.0-ALPHA-20250620"
inlinelogger = "1.0.6"
log4j = "2.23.1"
bouncycastle = "1.70"
java-jwt = "4.4.0"
jackson = "2.17.2"
flatlaf = "3.5"
miglayout = "11.4"
clikt = "4.2.2"
classgraph = "4.8.174"
okhttp3 = "4.12.0"
openrs2 = "0.1.0-SNAPSHOT"
swingx = "1.6.5-1"
shadowjar = "7.1.2"
zip4j = "2.11.5"
junixsocket = "2.10.0"
gson = "2.11.0"
aws-sdk-kotlin = "1.3.13"
jaxb-api = "2.3.1"
jopt-simple = "5.0.1"

[libraries]
netty-bom = { module = "io.netty:netty-bom", version.ref = "netty" }
netty-buffer = { module = "io.netty:netty-buffer" }
netty-transport = { module = "io.netty:netty-transport" }
netty-handler = { module = "io.netty:netty-handler" }
netty-codec-http = { module = "io.netty:netty-codec-http" }
rsprot-buffer = { module = "net.rsprot:buffer", version.ref = "rsprot" }
rsprot-compression = { module = "net.rsprot:compression", version.ref = "rsprot" }
rsprot-crypto = { module = "net.rsprot:crypto", version.ref = "rsprot" }
rsprot-protocol = { module = "net.rsprot:protocol", version.ref = "rsprot" }
log4j-bom = { module = "org.apache.logging.log4j:log4j-bom", version.ref = "log4j" }
log4j-core = { module = "org.apache.logging.log4j:log4j-core" }
log4j-api = { module = "org.apache.logging.log4j:log4j-api" }
log4j-slf4j-impl = { module = "org.apache.logging.log4j:log4j-slf4j2-impl" }
inline-logger = { module = "com.michael-bull.kotlin-inline-logger:kotlin-inline-logger", version.ref = "inlinelogger" }
bouncycastle-pkix = { module = "org.bouncycastle:bcpkix-jdk15on", version.ref = "bouncycastle" }
bouncycastle-provider = { module = "org.bouncycastle:bcprov-jdk15on", version.ref = "bouncycastle" }
java-jwt = { module = "com.auth0:java-jwt", version.ref = "java-jwt" }
jackson-annotations = { module = "com.fasterxml.jackson.core:jackson-annotations", version.ref = "jackson" }
jackson-dataformat-yaml = { module = "com.fasterxml.jackson.dataformat:jackson-dataformat-yaml", version.ref = "jackson" }
jackson-module-kotlin = { module = "com.fasterxml.jackson.module:jackson-module-kotlin", version.ref = "jackson" }
flatlaf = { module = "com.formdev:flatlaf", version.ref = "flatlaf" }
flatlaf-extras = { module = "com.formdev:flatlaf-extras", version.ref = "flatlaf" }
flatlaf-intellij-themes = { module = "com.formdev:flatlaf-intellij-themes", version.ref = "flatlaf" }
mig-layout = { module = "com.miglayout:miglayout-swing", version.ref = "miglayout" }
clikt = { module = "com.github.ajalt.clikt:clikt-jvm", version.ref = "clikt" }
classgraph = { module = "io.github.classgraph:classgraph", version.ref = "classgraph" }
okhttp3 = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp3" }
gson = { module = "com.google.code.gson:gson", version.ref = "gson" }
openrs2-buffer = { module = "org.openrs2:openrs2-buffer", version.ref = "openrs2"}
openrs2-cache = { module = "org.openrs2:openrs2-cache", version.ref = "openrs2"}
swingx = { module = "org.swinglabs.swingx:swingx-all", version.ref = "swingx" }
zip4j = { module = "net.lingala.zip4j:zip4j", version.ref = "zip4j" }
junixsocket = { module = "com.kohlschutter.junixsocket:junixsocket-core", version.ref = "junixsocket"}
aws-sdk-kotlin-s3 = { module = "aws.sdk.kotlin:s3", version.ref = "aws-sdk-kotlin" }
jaxb-api = { module = "javax.xml.bind:jaxb-api", version.ref = "jaxb-api" }
jopt-simple = { module = "net.sf.jopt-simple:jopt-simple", version.ref = "jopt-simple" }

[plugins]
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
shadowjar = { id = "com.github.johnrengelman.shadow", version.ref = "shadowjar" }

[bundles]
log4j = ["log4j-core", "log4j-api", "log4j-slf4j-impl", "inline-logger"]
bouncycastle = ["bouncycastle-pkix", "bouncycastle-provider"]
jackson = ["jackson-annotations", "jackson-dataformat-yaml", "jackson-module-kotlin"]
openrs2 = ["openrs2-buffer", "openrs2-cache"]
flatlaf = [ "flatlaf", "flatlaf-extras", "flatlaf-intellij-themes" ]
