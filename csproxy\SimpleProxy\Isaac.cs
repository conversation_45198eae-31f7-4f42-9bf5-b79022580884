namespace SimpleProxy;

// Minimal ISAAC PRNG port sufficient for OSRS opcode cipher
// Based on public domain reference; returns 32-bit results via NextInt()
public sealed class Isaac
{
    private const int Size = 256;
    private const int Mask = (Size - 1) << 2;

    private readonly uint[] _mem = new uint[Size];
    private readonly uint[] _rsl = new uint[Size];
    private int _count = 0;
    private uint _a, _b, _c;

    public Isaac(uint[] seed)
    {
        Init(seed);
    }

    public int NextInt()
    {
        if (_count == 0)
        {
            IsaacShuffle();
            _count = Size;
        }
        return unchecked((int)_rsl[--_count]);
    }

    private void Init(uint[] seed)
    {
        _a = _b = _c = 0;
        uint a = 0x9e3779b9, b = 0x9e3779b9, c = 0x9e3779b9, d = 0x9e3779b9,
             e = 0x9e3779b9, f = 0x9e3779b9, g = 0x9e3779b9, h = 0x9e3779b9;

        void Mix()
        {
            a ^= b << 11; d += a; b += c;
            b ^= c >> 2;  e += b; c += d;
            c ^= d << 8;  f += c; d += e;
            d ^= e >> 16; g += d; e += f;
            e ^= f << 10; h += e; f += g;
            f ^= g >> 4;  a += f; g += h;
            g ^= h << 8;  b += g; h += a;
            h ^= a >> 9;  c += h; a += b;
        }

        for (int i = 0; i < 4; i++) Mix();

        // Seed
        int n = seed?.Length ?? 0;
        for (int i = 0; i < Size; i += 8)
        {
            a += Get(seed, i + 0); b += Get(seed, i + 1);
            c += Get(seed, i + 2); d += Get(seed, i + 3);
            e += Get(seed, i + 4); f += Get(seed, i + 5);
            g += Get(seed, i + 6); h += Get(seed, i + 7);
            Mix();
            _mem[i + 0] = a; _mem[i + 1] = b; _mem[i + 2] = c; _mem[i + 3] = d;
            _mem[i + 4] = e; _mem[i + 5] = f; _mem[i + 6] = g; _mem[i + 7] = h;
        }

        // Second pass to thoroughly mix
        for (int i = 0; i < Size; i += 8)
        {
            a += _mem[i + 0]; b += _mem[i + 1];
            c += _mem[i + 2]; d += _mem[i + 3];
            e += _mem[i + 4]; f += _mem[i + 5];
            g += _mem[i + 6]; h += _mem[i + 7];
            Mix();
            _mem[i + 0] = a; _mem[i + 1] = b; _mem[i + 2] = c; _mem[i + 3] = d;
            _mem[i + 4] = e; _mem[i + 5] = f; _mem[i + 6] = g; _mem[i + 7] = h;
        }

        IsaacShuffle();
        _count = Size;
    }

    private static uint Get(uint[] seed, int i)
    {
        if (seed == null || i >= seed.Length) return 0u;
        return seed[i];
    }

    private void IsaacShuffle()
    {
        _c++; _b += _c;
        for (int i = 0; i < Size; i++)
        {
            uint x = _mem[i];
            switch (i & 3)
            {
                case 0: _a ^= (_a << 13); break;
                case 1: _a ^= (_a >> 6);  break;
                case 2: _a ^= (_a << 2);  break;
                case 3: _a ^= (_a >> 16); break;
            }
            _a += _mem[(i + 128) & 255];
            uint y = _mem[i] = _mem[(x >> 2) & 255] + _a + _b;
            _rsl[i] = _b = _mem[(y >> 10) & 255] + x;
        }
    }
}

