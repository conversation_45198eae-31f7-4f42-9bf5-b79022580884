using System.Collections.Generic;

namespace SimpleProxy;

public enum PacketSizeKind
{
    Fixed,
    VarByte,
    VarShort
}

public static class GamePacket
{
    // Map of opcode -> (kind, size)
    // If kind == Fixed, size is the fixed payload length (excluding opcode)
    // If kind == VarByte/VarShort, size is ignored and length is read from the stream
    private static readonly Dictionary<int, (PacketSizeKind kind, int size)> _sizes = new();

    public static bool HasAny() => _sizes.Count > 0;

    // Add or remove freely; pipeline must tolerate missing entries
    public static void Register(int opcode, PacketSizeKind kind, int size = 0)
    {
        _sizes[opcode & 0xFF] = (kind, size);
    }

    public static bool TryGet(int opcode, out PacketSizeKind kind, out int size)
    {
        if (_sizes.TryGetValue(opcode & 0xFF, out var v))
        {
            kind = v.kind; size = v.size; return true;
        }
        kind = default; size = 0; return false;
    }
}

