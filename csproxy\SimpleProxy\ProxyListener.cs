using System.Net;
using System.Net.Sockets;
using Org.BouncyCastle.Crypto.Parameters;
using SimpleProxy.Interceptors.PreLogin;
using SimpleProxy.Interceptors.Game;

namespace SimpleProxy;

public sealed class ProxyListener
{
    private readonly string _listenHost;
    private readonly int _listenPort;
    private readonly string _upstreamHost;
    private readonly int _upstreamPort;
    private readonly RsaPrivateCrtKeyParameters _priv;

    public ProxyListener(string listenHost, int listenPort, string upstreamHost, int upstreamPort, Org.BouncyCastle.Crypto.AsymmetricCipherKeyPair kp)
    {
        _listenHost = listenHost;
        _listenPort = listenPort;
        _upstreamHost = upstreamHost;
        _upstreamPort = upstreamPort;
        _priv = (RsaPrivateCrtKeyParameters)kp.Private;
    }

    public async Task RunAsync(CancellationToken cancellationToken = default)
    {
        var listener = new TcpListener(IPAddress.Parse(_listenHost), _listenPort);
        listener.Start();
        Console.WriteLine($"[SimpleProxy] Listening on {_listenHost}:{_listenPort} -> {_upstreamHost}:{_upstreamPort}");

        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                var client = await listener.AcceptTcpClientAsync(cancellationToken);
                _ = HandleClientAsync(client);
            }
        }
        finally
        {
            listener.Stop();
        }
    }

    private async Task HandleClientAsync(TcpClient client)
    {
        Console.WriteLine($"[SimpleProxy] Client connected: {client.Client.RemoteEndPoint}");
        using var upstream = new TcpClient();
        try
        {
            await upstream.ConnectAsync(_upstreamHost, _upstreamPort);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[SimpleProxy] Failed to connect upstream: {ex.Message}");
            client.Close();
            return;
        }

        using var clientStream = client.GetStream();
        using var upstreamStream = upstream.GetStream();

        var cts = new CancellationTokenSource();
        // Start server->client pump immediately so the client gets server handshake while we watch for the login packet in client->server
        var serverToClientPump = PumpAsync(upstreamStream, clientStream, cts.Token);

        Session? session = null;
        try
        {
            session = await PreLogin.RunAsync(clientStream, upstreamStream, _priv);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[SimpleProxy] Login intercept error: {ex.Message}");
            cts.Cancel();
            client.Close();
            upstream.Close();
            return;
        }

        // After pre-login: try simple client->server decoder; if not available or it bails, fall back to raw piping
        if (session != null)
        {
            var result = await ClientGame.RunAsync(clientStream, upstreamStream, session, cts.Token);
            if (result == GameDecodeResult.RanToEnd)
            {
                // client stream ended; wait for server->client to finish
                await serverToClientPump;
                cts.Cancel();
                client.Close();
                upstream.Close();
                Console.WriteLine("[SimpleProxy] Connection closed");
                return;
            }
            // else either None or BailedToRaw -> continue with raw duplex piping
        }

        var clientToServerPump = PumpAsync(clientStream, upstreamStream, cts.Token);
        await Task.WhenAny(clientToServerPump, serverToClientPump);
        cts.Cancel();
        client.Close();
        upstream.Close();
        Console.WriteLine("[SimpleProxy] Connection closed");
    }

    private static async Task PumpAsync(NetworkStream from, NetworkStream to, CancellationToken ct, bool log = false)
    {
        var buf = new byte[64 * 1024];
        try
        {
            while (!ct.IsCancellationRequested)
            {
                int n = await from.ReadAsync(buf.AsMemory(), ct);
                if (n <= 0) break;
                var hex = BitConverter.ToString(buf, 0, n).Replace("-", " ");
                if (log) Console.WriteLine($"{n} > {hex}");
                await to.WriteAsync(buf.AsMemory(0, n), ct);
                await to.FlushAsync(ct);
            }
        }
        catch
        {
            // ignore
        }
    }
}

