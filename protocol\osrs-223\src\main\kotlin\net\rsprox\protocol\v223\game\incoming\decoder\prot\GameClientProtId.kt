package net.rsprox.protocol.v223.game.incoming.decoder.prot

internal object GameClientProtId {
    const val RESUME_PAUSEBUTTON = 0
    const val EVENT_KEYBOARD = 1
    const val HISCORE_REQUEST = 2
    const val RESUME_P_STRINGDIALOG = 3
    const val SOUND_JINGLEEND = 4
    const val OPNPC6 = 5
    const val OPHELD6 = 6
    const val CLIENT_CHEAT = 7
    const val AFFINEDCLANSETTINGS_ADDBANNED_FROMCHANNEL = 8
    const val OPOBJ3 = 9
    const val OPOBJT = 10
    const val AFFINEDCLANSETTINGS_SETMUTED_FROMCHANNEL = 11
    const val REFLECTION_CHECK_REPLY = 12
    const val FRIENDLIST_DEL = 13
    const val OPOBJ5 = 14
    const val IF_BUTTONT = 15
    const val IF_BUTTON5 = 16
    const val IF_BUTTON6 = 17
    const val EVENT_MOUSE_CLICK_V1 = 18
    const val OPLOC3 = 19
    const val IGNORELIST_ADD = 20
    const val UPDATE_PLAYER_MODEL_V1 = 21
    const val WINDOW_STATUS = 22
    const val OPPLAYER8 = 23
    const val MESSAGE_PUBLIC = 24
    const val DETECT_MODIFIED_CLIENT = 25
    const val IF_BUTTON2 = 26
    const val OPOBJ2 = 27
    const val OPOBJU = 28
    const val IF_BUTTON1 = 29
    const val IF_CRMVIEW_CLICK = 30
    const val RESUME_P_NAMEDIALOG = 31
    const val OPNPC1 = 32
    const val IF_BUTTON10 = 33
    const val OPNPC3 = 34
    const val OPLOCT = 35
    const val OPNPC5 = 36
    const val CLANCHANNEL_FULL_REQUEST = 37
    const val OPLOC1 = 38
    const val FRIENDCHAT_SETRANK = 39
    const val OPPLAYERU = 40
    const val IF_BUTTON4 = 41
    const val CONNECTION_TELEMETRY = 42
    const val IF_BUTTOND = 43
    const val NO_TIMEOUT = 44
    const val OPPLAYER2 = 45
    const val OPPLAYER4 = 46
    const val OPOBJ4 = 47
    const val OPLOC6 = 48
    const val BUG_REPORT = 49
    const val OPPLAYER7 = 50
    const val EVENT_NATIVE_MOUSE_MOVE = 51
    const val CLANSETTINGS_FULL_REQUEST = 52
    const val IF_BUTTON8 = 53
    const val SET_CHATFILTERSETTINGS = 54
    const val OPNPCT = 55
    const val MOVE_GAMECLICK = 56
    const val OPLOC5 = 57
    const val IF_BUTTON7 = 58
    const val RESUME_P_OBJDIALOG = 59
    const val TELEPORT = 60
    const val CLICKWORLDMAP = 61
    const val MOVE_MINIMAPCLICK = 62
    const val MEMBERSHIP_PROMOTION_ELIGIBILITY = 63
    const val EVENT_MOUSE_MOVE = 64
    const val OPLOC2 = 65
    const val OPPLAYER5 = 66
    const val OPPLAYER1 = 67
    const val OPOBJ1 = 68
    const val OPOBJ6 = 69
    const val MAP_BUILD_COMPLETE = 70
    const val SEND_SNAPSHOT = 71
    const val FRIENDLIST_ADD = 72
    const val IF_BUTTON = 73
    const val MESSAGE_PRIVATE = 74
    const val EVENT_APPLET_FOCUS = 75
    const val OPPLAYERT = 76
    const val IF_BUTTON9 = 77
    const val IGNORELIST_DEL = 78
    const val EVENT_MOUSE_SCROLL = 79
    const val FRIENDCHAT_KICK = 80
    const val RESUME_P_COUNTDIALOG = 81
    const val SEND_PING_REPLY = 82
    const val OCULUS_LEAVE = 83
    const val OPNPC4 = 84
    const val OPLOCU = 85
    const val EVENT_CAMERA_POSITION = 86
    const val FRIENDCHAT_JOIN_LEAVE = 87
    const val OPNPCU = 88
    const val OPPLAYER6 = 89
    const val IDLE = 90
    const val OPLOC4 = 91
    const val OPNPC2 = 92
    const val OPPLAYER3 = 93
    const val EVENT_NATIVE_MOUSE_CLICK = 94
    const val CLOSE_MODAL = 95
    const val IF_BUTTON3 = 96
    const val CLANCHANNEL_KICKUSER = 97
}
