{"version": 3, "targets": {"net8.0": {"BouncyCastle.Cryptography/2.6.2": {"type": "package", "compile": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"related": ".xml"}}}}}, "libraries": {"BouncyCastle.Cryptography/2.6.2": {"sha512": "7oWOcvnntmMKNzDLsdxAYqApt+AjpRpP2CShjMfIa3umZ42UQMvH0tl1qAliYPNYO6vTdcGMqnRrCPmsfzTI1w==", "type": "package", "path": "bouncycastle.cryptography/2.6.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "bouncycastle.cryptography.2.6.2.nupkg.sha512", "bouncycastle.cryptography.nuspec", "lib/net461/BouncyCastle.Cryptography.dll", "lib/net461/BouncyCastle.Cryptography.xml", "lib/net6.0/BouncyCastle.Cryptography.dll", "lib/net6.0/BouncyCastle.Cryptography.xml", "lib/netstandard2.0/BouncyCastle.Cryptography.dll", "lib/netstandard2.0/BouncyCastle.Cryptography.xml", "packageIcon.png"]}}, "projectFileDependencyGroups": {"net8.0": ["BouncyCastle.Cryptography >= 2.6.2"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\rsprox\\csproxy\\SimpleProxy\\SimpleProxy.csproj", "projectName": "SimpleProxy", "projectPath": "C:\\Users\\<USER>\\Desktop\\rsprox\\csproxy\\SimpleProxy\\SimpleProxy.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\rsprox\\csproxy\\SimpleProxy\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BouncyCastle.Cryptography": {"target": "Package", "version": "[2.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.14, 8.0.14]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[8.0.14, 8.0.14]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.14, 8.0.14]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.14, 8.0.14]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.202/PortableRuntimeIdentifierGraph.json"}}}}