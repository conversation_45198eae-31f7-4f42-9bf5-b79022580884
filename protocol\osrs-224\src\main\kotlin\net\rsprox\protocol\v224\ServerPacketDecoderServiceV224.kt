package net.rsprox.protocol.v224

import net.rsprot.buffer.JagByteBuf
import net.rsprot.compression.HuffmanCodec
import net.rsprot.protocol.message.IncomingMessage
import net.rsprox.cache.api.CacheProvider
import net.rsprox.protocol.ServerPacketDecoder
import net.rsprox.protocol.session.Session
import net.rsprox.protocol.v224.game.outgoing.decoder.prot.ServerMessageDecoderRepository

public class ServerPacketDecoderServiceV224(
    huffmanCodec: <PERSON><PERSON>manCodec,
    cache: CacheProvider,
) : ServerPacketDecoder {
    @OptIn(ExperimentalStdlibApi::class)
    private val repository =
        ServerMessageDecoderRepository.build(
            huffmanCodec,
            cache,
        )

    override fun decode(
        opcode: Int,
        payload: JagByteBuf,
        session: Session,
    ): IncomingMessage {
        return repository
            .getDecoder(opcode)
            .decode(payload, session)
    }
}
