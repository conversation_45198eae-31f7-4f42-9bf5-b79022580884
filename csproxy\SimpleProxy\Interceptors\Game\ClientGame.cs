using System.Buffers;
using System.Net.Sockets;
using SimpleProxy;

namespace SimpleProxy.Interceptors.Game;

public enum GameDecodeResult
{
    None,        // did not start (no sizes registered)
    RanToEnd,    // consumed until EOF/cancel
    BailedToRaw  // encountered unknown packet and switched to raw
}

public static class ClientGame
{
    public static async Task<GameDecodeResult> RunAsync(NetworkStream client, NetworkStream upstream, Session session, CancellationToken ct = default)
    {
        // If we don't have any packet sizes registered, do nothing and let caller fall back to raw piping
        // if (!GamePacket.HasAny())
        //     return GameDecodeResult.None;

        try
        {
            var lenHdr = new byte[2];
            while (!ct.IsCancellationRequested)
            {
                var one = new byte[1];
                int b = await client.ReadAsync(one.AsMemory(0, 1), ct);
                if (b <= 0) break;
                byte encOpcode = one[0];
                int mod = session.InboundIsaac.NextInt();
                int opcode = (encOpcode - mod) & 0xFF;

                if (!GamePacket.TryGet(opcode, out var kind, out var fixedSize))
                {
                    await upstream.WriteAsync(new ReadOnlyMemory<byte>(new[] { encOpcode }), ct);
                    await upstream.FlushAsync(ct);
                    Console.WriteLine($"[Game] Unknown opcode 0x{opcode:X2} (no size). Falling back to raw.");
                    return GameDecodeResult.BailedToRaw;
                }

                Console.WriteLine("[Game] Packet opcode: " + opcode);

                // Buffer to forward exactly what we read
                var fwd = new ArrayBufferWriter<byte>(1 + 2 + fixedSize);
                fwd.GetSpan(1)[0] = encOpcode; fwd.Advance(1);

                int payloadLen;
                switch (kind)
                {
                    case PacketSizeKind.Fixed:
                        payloadLen = fixedSize;
                        break;
                    case PacketSizeKind.VarByte:
                        if (!await ReadExactAsync(client, lenHdr, 0, 1, ct)) return GameDecodeResult.RanToEnd;
                        fwd.Write(new ReadOnlySpan<byte>(lenHdr, 0, 1));
                        payloadLen = lenHdr[0] & 0xFF;
                        break;
                    case PacketSizeKind.VarShort:
                        if (!await ReadExactAsync(client, lenHdr, 0, 2, ct)) return GameDecodeResult.RanToEnd;
                        fwd.Write(new ReadOnlySpan<byte>(lenHdr, 0, 2));
                        payloadLen = ((lenHdr[0] & 0xFF) << 8) | (lenHdr[1] & 0xFF);
                        break;
                    default:
                        payloadLen = 0;
                        break;
                }

                if (payloadLen > 0)
                {
                    var payload = ArrayPool<byte>.Shared.Rent(payloadLen);
                    try
                    {
                        if (!await ReadExactAsync(client, payload, 0, payloadLen, ct)) return GameDecodeResult.RanToEnd;
                        fwd.Write(new ReadOnlySpan<byte>(payload, 0, payloadLen));
                    }
                    finally
                    {
                        ArrayPool<byte>.Shared.Return(payload);
                    }
                }

                Console.WriteLine($"[Game] C->S opcode=0x{opcode:X2} len={payloadLen}");
                await upstream.WriteAsync(fwd.WrittenMemory, ct);
                await upstream.FlushAsync(ct);
            }
        }
        catch (OperationCanceledException)
        {
            // ignore
        }
        return GameDecodeResult.RanToEnd;
    }

    private static async Task<bool> ReadExactAsync(NetworkStream s, byte[] buf, int off, int len, CancellationToken ct)
    {
        int read = 0;
        while (read < len)
        {
            int n = await s.ReadAsync(buf.AsMemory(off + read, len - read), ct);
            if (n <= 0) return false;
            read += n;
        }
        return true;
    }
}

