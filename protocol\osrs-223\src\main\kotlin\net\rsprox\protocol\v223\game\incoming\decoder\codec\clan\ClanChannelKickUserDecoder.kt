package net.rsprox.protocol.v223.game.incoming.decoder.codec.clan

import net.rsprot.buffer.JagByteBuf
import net.rsprot.protocol.ClientProt
import net.rsprot.protocol.metadata.Consistent
import net.rsprox.protocol.ProxyMessageDecoder
import net.rsprox.protocol.game.incoming.model.clan.ClanChannelKickUser
import net.rsprox.protocol.session.Session
import net.rsprox.protocol.v223.game.incoming.decoder.prot.GameClientProt

@Consistent
internal class ClanChannelKickUserDecoder : ProxyMessageDecoder<ClanChannelKickUser> {
    override val prot: ClientProt = GameClientProt.CLANCHANNEL_KICKUSER

    override fun decode(
        buffer: Jag<PERSON><PERSON><PERSON>uf,
        session: Session,
    ): ClanChannel<PERSON>ickUser {
        val clanId = buffer.g1()
        val memberIndex = buffer.g2()
        val name = buffer.gjstr()
        return <PERSON><PERSON>han<PERSON><PERSON><PERSON><PERSON>ser(
            name,
            clanId,
            memberIndex,
        )
    }
}
