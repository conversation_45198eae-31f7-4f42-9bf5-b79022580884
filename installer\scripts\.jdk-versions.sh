# windows x64
WIN64_VERSION=11.0.24+8
WIN64_CHKSUM=e0181952006f9779551511d1f449ca33269a58b7b8802f001fd4ceeff2fd01f3
WIN64_LINK=https://github.com/adoptium/temurin11-binaries/releases/download/jdk-11.0.24%2B8/OpenJDK11U-jdk_x64_windows_hotspot_11.0.24_8.zip
# windows x86
WIN32_VERSION=11.0.24+8
WIN32_CHKSUM=7392740f4a4176d65f279f822c35df0d9a3845cefad19c521e58c0001d929308
WIN32_LINK=https://github.com/adoptium/temurin11-binaries/releases/download/jdk-11.0.24%2B8/OpenJDK11U-jdk_x86-32_windows_hotspot_11.0.24_8.zip
# windows aarch64
WIN_AARCH64_VERSION=11.0.24+8
WIN_AARCH64_CHKSUM=bac491e89078ba88e8a4c85fb1458c9821670126359101e555818623baf0432a
WIN_AARCH64_LINK=https://aka.ms/download-jdk/microsoft-jdk-11.0.24-windows-aarch64.zip
# mac x64
MAC_AMD64_VERSION=17.0.12+7
MAC_AMD64_CHKSUM=d5230eeec88739aa7133e4c8635bbd4ab226708c12deaafa13cf26b02bc8e8c4
MAC_AMD64_LINK=https://github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.12%2B7/OpenJDK17U-jdk_x64_mac_hotspot_17.0.12_7.tar.gz
# mac aarch64
MAC_AARCH64_VERSION=17.0.12+7
MAC_AARCH64_CHKSUM=d7910b1acaeb290c5c5da21811d2b2b8635f806612a2d6e8d1953b2f77580f78
MAC_AARCH64_LINK=https://github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.12%2B7/OpenJDK17U-jdk_aarch64_mac_hotspot_17.0.12_7.tar.gz
# linux x64
LINUX_AMD64_VERSION=11.0.24+8
LINUX_AMD64_CHKSUM=0e71a01563a5c7b9988a168b0c4ce720a6dff966b3c27bb29d1ded461ff71d0e
LINUX_AMD64_LINK=https://github.com/adoptium/temurin11-binaries/releases/download/jdk-11.0.24%2B8/OpenJDK11U-jdk_x64_linux_hotspot_11.0.24_8.tar.gz
# linux aarch64
LINUX_AARCH64_VERSION=11.0.24+8
LINUX_AARCH64_CHKSUM=04e21301fedc76841fb03929ac6cacfbbda30b5693acfd515a8f34d4a0cdeb28
LINUX_AARCH64_LINK=https://github.com/adoptium/temurin11-binaries/releases/download/jdk-11.0.24%2B8/OpenJDK11U-jdk_aarch64_linux_hotspot_11.0.24_8.tar.gz
