{"name": "Material Darker", "dark": true, "author": "<PERSON><PERSON><PERSON>", "colors": {"bg": "#212121", "bg30": "#21212130", "fg": "#C6C6C6", "text": "#727272", "selBg": "#404040", "selBg20": "#40404020", "selFg": "#FFFFFF", "activeFg": "#FFFFFF", "border": "#292929", "excl": "#323232", "second": "#292929", "dis": "#474747", "accent": "#DC8A00", "accent2": "#FF98002", "accent50": "#DC8A0078", "accent70": "#DC8A00B3", "cs": "#1A1A1A", "button": "#2A2A2A", "table": "#323232", "tree": "#2d2d2d", "hl": "#3F3F3F", "notif": "#1A1A1A", "hc": "#212121", "shadow": "undefined", "white": "#eeffff", "blue": "#82aaff", "red": "#f07178", "yellow": "#ffcb6b", "green": "#c3e88d", "gray": "#616161", "purple": "#c792ea", "orange": "#f78c6c"}, "editorScheme": "colors/Material Darker.xml", "ui": {"*": {"acceleratorForeground": "text", "acceleratorSelectionForeground": "text", "background": "bg", "borderColor": "border", "disabledBackground": "excl", "disabledBorderColor": "button", "disabledForeground": "dis", "disabledText": "dis", "focusColor": "hl", "focusedBorderColor": "accent", "foreground": "fg", "hoverBorderColor": "hl", "inactiveBackground": "excl", "inactiveForeground": "text", "infoForeground": "text", "lineSeparatorColor": "hl", "pressedBorderColor": "hl", "selectionBackground": "selBg", "selectionBackgroundInactive": "second", "selectionForeground": "selFg", "selectionInactiveBackground": "second", "separatorColor": "border"}, "activeCaption": "bg", "activeCaptionBorder": "bg", "activeCaptionText": "fg", "ActionButton": {"focusedBorderColor": "accent50", "hoverBackground": "accent50", "hoverBorderColor": "accent50", "hoverSeparatorColor": "button", "pressedBackground": "accent50", "pressedBorderColor": "accent50"}, "ActionsList": {"icon.gap": 12, "mnemonic.icon.gap": 12}, "ActionToolbar": {"background": "hc"}, "AppInspector.GraphNode": {"background": "second", "borderColor": "border", "focusedBorderColor": "accent"}, "AssignedMnemonic": {"background": "hl", "borderColor": "selBg", "foreground": "activeFg"}, "Autocomplete": {"selectionBackground": "selBg", "selectionUnfocus": "hl"}, "AvailableMnemonic": {"background": "button", "borderColor": "button", "foreground": "fg"}, "BigSpinner.background": "bg", "Bookmark": {"iconBackground": "accent", "Mnemonic": {"iconBackground": "hl", "iconBorderColor": "hl", "iconForeground": "fg"}}, "BookmarkMnemonicAssigned": {"background": "hl", "borderColor": "selBg", "foreground": "selFg"}, "BookmarkMnemonicAvailable": {"background": "button", "borderColor": "button", "foreground": "fg"}, "BookmarkMnemonicCurrent": {"background": "accent", "borderColor": "accent", "foreground": "selFg"}, "BookmarkMnemonicIcon": {"background": "hl", "borderColor": "hl", "foreground": "fg"}, "BookmarkIcon.background": "accent", "Borders.ContrastBorderColor": "bg", "Borders.color": "border", "Breakpoint.iconHoverAlpha": 0.35, "Button": {"arc": 8, "background": "bg", "darcula": {"borderColor": "button", "defaultBorderColor": "button", "defaultEndColor": "button", "defaultFocusedBorderColor": "table", "defaultOutlineColor": "button", "defaultStartColor": "button", "disabledBorderColor": "button", "disabledOutlineColor": "border", "disabledText.shadow": "bg", "endColor": "button", "focusedBorderColor": "table", "outlineColor": "button", "selectedButtonForeground": "selFg", "selection.color1": "table", "selection.color2": "table", "shadowColor": "shadow", "smallComboButtonBackground": "button", "startColor": "button"}, "default": {"borderColor": "button", "endBackground": "table", "endBorderColor": "table", "focusColor": "accent", "focusedBorderColor": "accent", "foreground": "selFg", "shadowColor": "shadow", "startBackground": "table", "startBorderColor": "table"}, "disabledBackground": "excl", "disabledBorderColor": "border", "disabledText": "dis", "endBackground": "table", "endBorderColor": "button", "focus": "hl", "focusedBorderColor": "accent", "foreground": "text", "highlight": "selFg", "mt.background": "button", "mt.color1": "button", "mt.color2": "button", "mt.foreground": "text", "mt.selectedForeground": "selFg", "mt.selection.color1": "table", "mt.selection.color2": "table", "select": "button", "shadowColor": "shadow", "shadowWidth": 0, "startBackground": "table", "startBorderColor": "button", "ToolWindow": {"arc": 40}}, "Canvas": {"Tooltip.background": "notif", "Tooltip.borderColor": "border"}, "CheckBox": {"background": "bg", "background.selected": "accent", "borderColor": "bg", "borderColor.selected": "accent", "checkSignColor": "bg", "checkSignColor.selected": "bg", "checkSignColorDisabled": "bg", "checkSignColorDisabled.selected": "bg", "darcula": {"borderColor1": "fg", "checkSignColorDisabled": "dis", "disabledBorderColor1": "dis", "disabledBorderColor2": "dis", "inactiveFillColor": "hl"}, "disabledBorderColor": "bg", "disabledBorderColor.selected": "accent", "disabledText": "dis", "focused.background": "bg", "focused.background.selected": "accent", "focusedArmed.background": "bg", "focusedArmed.background.selected": "accent", "foreground": "fg", "inactiveFillColor": "bg", "inactiveFillColor.selected": "accent", "select": "accent", "shadowColor": "bg", "shadowColorDisabled": "bg"}, "Checkbox": {"Background.Default": "bg", "Background.Default.Dark": "bg", "Background.Disabled": "second", "Background.Disabled.Dark": "second", "Background.Selected": "accent", "Background.Selected.Dark": "accent", "Border.Default": "hl", "Border.Default.Dark": "hl", "Border.Disabled": "second", "Border.Disabled.Dark": "second", "Border.Selected": "accent", "Border.Selected.Dark": "accent", "Focus.Thin.Default": "bg", "Focus.Thin.Default.Dark": "bg", "Focus.Wide.Default": "bg", "Focus.Wide.Default.Dark": "bg", "Focus.Thin.Selected": "accent", "Focus.Thin.Selected.Dark": "accent", "Focus.Wide.Selected": "accent", "Focus.Wide.Selected.Dark": "accent"}, "CheckBoxMenuItem": {"acceleratorForeground": "text", "acceleratorSelectionForeground": "text", "background": "bg", "disabledBackground": "bg", "disabledForeground": "dis", "foreground": "fg", "selectionBackground": "selBg", "selectionForeground": "activeFg"}, "CodeWithMe": {"Avatar.foreground": "fg", "AccessDisabled.accessDot": "dis", "AccessEnabled": {"accessDot": "accent", "dropdownBorder": "second", "pillBackground": "second"}, "Users": {"1": {"Background": "green", "Foreground": "fg"}, "2": {"Background": "blue", "Foreground": "fg"}, "3": {"Background": "red", "Foreground": "fg"}, "4": {"Background": "purple", "Foreground": "fg"}, "5": {"Background": "yellow", "Foreground": "fg"}, "6": {"Background": "orange", "Foreground": "fg"}}}, "ColorChooser": {"background": "bg", "foreground": "fg", "swatchesDefaultRecentColor": "fg"}, "ComboBoxButton.background": "button", "ComboBox": {"ArrowButton": {"background": "button", "disabledIconColor": "dis", "iconColor": "fg", "nonEditableBackground": "hc"}, "PopupBackground": "cs", "arrowFillColor": "hc", "background": "hc", "buttonBackground": "button", "darcula": {"arrowButtonBackground": "hc", "arrowButtonDisabledForeground": "dis", "arrowButtonForeground": "fg", "disabledArrowButtonBackground": "excl", "editable.arrowButtonBackground": "hc", "hoveredArrowButtonForeground": "accent", "nonEditableBackground": "bg"}, "disabledBackground": "excl", "disabledForeground": "dis", "foreground": "fg", "modifiedItemForeground": "accent", "nonEditableBackground": "bg", "padding": "5,5,5,5", "selectionBackground": "table", "selectionForeground": "activeFg"}, "ComboPopup.border": "1,1,1,1,#FF9800", "CompletionPopup": {"Advertiser": {"background": "bg", "foreground": "fg", "borderInsets": "8,12,8,12"}, "background": "second", "foreground": "fg", "grayForeground": "text", "grayedForeground": "text", "infoForeground": "text", "matchForeground": "accent", "matchSelectedForeground": "accent", "matchSelectionForeground": "accent", "nonFocusedState": "false", "selectedForeground": "selFg", "selectedGrayedForeground": "selFg", "selectionBackground": "cs", "selectionForeground": "selFg", "selectionGrayForeground": "selFg", "selectionInactiveBackground": "hl", "selectionInactiveForeground": "text", "selectionInactiveInfoForeground": "text", "selectionInfoForeground": "selFg"}, "ComplexPopup": {"Header.background": "notif"}, "Component": {"arc": 4, "focusWidth": 2, "borderColor": "hl", "disabledBorderColor": "dis", "focusColor": "accent", "focusedBorderColor": "accent", "hoverIconColor": "accent", "iconColor": "text", "grayForeground": "text", "infoForeground": "text"}, "Content": {"background": "cs", "selectionBackground": "selBg", "selectionInactiveBackground": "fg"}, "control": "button", "controlLtHighlight": "selFg", "controlText": "text", "controlHighlight": "hl", "controlShadow": "excl", "controlDkShadow": "excl", "Counter": {"background": "accent", "foreground": "selFg"}, "CurrentMnemonic": {"background": "accent", "borderColor": "accent", "foreground": "selFg"}, "darcula": {"background": "bg", "foreground": "fg", "primary": "bg"}, "desktop": "hc", "Debugger": {"EvaluateExpression.background": "bg", "Variables": {"changedValueForeground": "accent", "collectingDataForeground": "text", "errorMessageForeground": "red", "evaluatingExpressionForeground": "text", "exceptionForeground": "yellow", "modifyingValueForeground": "accent", "valueForeground": "accent", "typeForeground": "text"}}, "DebuggerTabs": {"active.background": "hl", "selectedBackground": "hl", "underlinedTabBackground": "table", "underlineHeight": 2}, "DebuggerPopup": {"borderColor": "bg"}, "DefaultTabs": {"background": "bg", "borderColor": "bg", "hoverBackground": "table", "hoverColor": "hl", "hoverMaskColor": "hl", "inactiveColoredFileBackground": "button", "inactiveColoredTabBackground": "bg", "inactiveMaskColor": "hc", "inactiveUnderlineColor": "accent", "underlineColor": "accent", "underlineHeight": 3, "underlinedTabBackground": "table", "underlinedTabForeground": "selFg"}, "Desktop.background": "bg", "Dialog": {"titleColor": "bg"}, "DialogWrapper": {"southPanelBackground": "bg", "southPanelDivider": "bg"}, "DragAndDrop": {"areaBackground": "tree", "areaBorderColor": "bg", "areaForeground": "fg", "backgroundBorderColor": "bg", "backgroundColor": "bg", "borderColor": "accent", "foregroundColor": "fg", "rowBackground": "accent70"}, "dropArea.base": "accent", "Editor": {"background": "hc", "foreground": "fg", "shortcutForeground": "text", "SearchField": {"background": "cs", "borderInsets": "8,12,8,12"}, "Toolbar": {"borderColor": "border"}}, "EditorGroupsTabs": {"background": "bg", "borderColor": "second", "hoverBackground": "hl", "hoverColor": "hl", "inactiveUnderlineColor": "accent", "underlineColor": "accent", "underlinedTabBackground": "table", "underlinedTabForeground": "fg"}, "EditorPane": {"background": "hc", "caretForeground": "accent", "foreground": "fg", "inactiveBackground": "bg", "inactiveForeground": "dis", "selectionBackground": "selBg", "selectionForeground": "activeFg", "splitBorder": "border"}, "EditorTabs": {"active.background": "table", "active.foreground": "fg", "active.underlineColor": "accent", "background": "bg", "borderColor": "second", "foreground": "fg", "hoverBackground": "hl", "hoverColor": "hl", "hoverMaskColor": "hl", "inactive.maskColor": "bg", "inactiveColoredFileBackground": "bg", "inactiveMaskColor": "bg", "inactiveUnderlineColor": "dis", "selectedBackground": "table", "selectedForeground": "fg", "tabInsets": "-10,10,-10,10", "underlineColor": "accent", "underlineHeight": 3, "underlineArc": 4, "underlinedTabBackground": "table", "underlinedTabForeground": "selFg"}, "FileColor": {"Blue": "#004BA0", "Green": "#387002", "Orange": "#B53D00", "Rose": "#A00037", "Violet": "#4D2C91", "Yellow": "excl", "Gray": "excl", "excluded": "excl"}, "FlameGraph": {"JVMBackground": "#89DDF7", "JVMFocusBackground": "#82AAFF", "JVMFocusSearchNotMatchedBackground": "#AB7967", "JVMSearchNotMatchedBackground": "#FF5370", "nativeBackground": "#FFCB6B", "nativeFocusBackground": "#F78C6C", "nativeFocusSearchNotMatchedBackground": "#BB80B3", "nativeSearchNotMatchedBackground": "#C792EA"}, "Focus": {"Color": "accent50", "borderColor": "accent50", "color": "accent50", "defaultButtonBorderColor": "accent"}, "FormattedTextField": {"background": "cs", "caretForeground": "accent", "foreground": "fg", "inactiveBackground": "button", "inactiveForeground": "dis", "selectionBackground": "selBg", "selectionForeground": "activeFg"}, "Git.Log.Ref": {"LocalBranch": "accent", "Other": "text", "RemoteBranch": "fg", "Tag": "text"}, "Github.List.tallRow": {"foreground": "fg", "secondary.foreground": "text", "selectionBackground": "selBg", "selectionBackground.unfocused": "hl", "selectionForeground": "selFg", "selectionForeground.unfocused": "selFg"}, "GotItTooltip": {"background": "notif", "borderColor": "notif", "endBackground": "button", "endBorderColor": "button", "foreground": "fg", "linkForeground": "accent", "shortcutForeground": "text", "startBackground": "button", "startBorderColor": "button"}, "Group": {"disabledSeparatorColor": "border", "separatorColor": "border"}, "Gutter": {"VcsChanges.width": 4}, "GutterTooltip": {"borderColor": "bg", "infoForeground": "text", "lineSeparatorColor": "bg"}, "HeaderColor": {"active": "bg", "inactive": "cs"}, "HelpTooltip": {"background": "bg", "backgroundColor": "bg", "borderColor": "border", "defaultTextBorderInsets": "16,16,16,16", "foreground": "fg", "infoForeground": "text", "shortcutForeground": "text", "shortcutTextColor": "text", "smallTextBorderInsets": "8,12,8,12", "textColor": "fg", "verticalGap": 8}, "Hg.Log.Ref": {"Branch": "accent", "ClosedBranch": "fg", "LocalTag": "text", "MqTag": "text", "Tag": "text"}, "Hyperlink.linkColor": "accent", "IconBadge": {"infoBackground": "accent"}, "IdeStatusBar.border": "4,4,4,4", "InformationHint.borderColor": "border", "inactiveCaption": "second", "inactiveCaptionBorder": "bg", "inactiveCaptionText": "text", "info": "text", "infoPanelForeground": "text", "infoText": "text", "InplaceRefactoringPopup": {"background": "bg", "borderColor": "bg"}, "intellijlaf": {"background": "bg", "foreground": "fg"}, "InternalFrame": {"activeTitleForeground": "fg", "background": "bg", "inactiveTitleBackground": "bg", "inactiveTitleForeground": "text"}, "Label": {"background": "bg", "disabledForeground": "dis", "disabledForegroundColor": "dis", "disabledShadow": "dis", "disabledText": "dis", "errorForeground": "accent", "foreground": "fg", "grayForeground": "text", "infoForeground": "text", "selectedDisabledForeground": "fg", "selectedForeground": "activeFg", "textForeground": "text"}, "Lesson": {"Badge": {"newLessonBackground": "accent", "newLessonForeground": "selFg"}, "shortcutBackground": "second", "stepNumberForeground": "text", "Tooltip": {"background": "notif", "borderColor": "notif", "foreground": "fg", "spanBackground": "button", "spanForeground": "fg", "stepNumberForeground": "text"}}, "link": "accent", "Link": {"activeForeground": "accent", "focusedBorderColor": "accent", "hoverForeground": "accent", "pressedForeground": "accent", "secondaryForeground": "text", "visitedForeground": "accent"}, "link.foreground": "accent", "link.hover.foreground": "accent", "link.pressed.foreground": "accent", "link.visited.foreground": "accent", "List": {"background": "bg", "Button": {"hoverBackground": "hl", "leftRightInset": 8, "separatorColor": "border", "separatorInset": 4}, "foreground": "fg", "hoverBackground": "hl", "hoverInactiveBackground": "table", "Line.hoverBackground": "selBg", "rowHeight": "28", "selectionBackground": "tree", "selectionForeground": "selFg", "selectionInactiveBackground": "table", "selectionInactiveForeground": "activeFg", "Tag": {"background": "button", "foreground": "fg"}}, "LiveIndicator": {"color": "accent"}, "macOSWindow.Title": {"height": 30, "heightSmall": 20}, "MainMenu": {"background": "hc", "foreground": "fg", "selectionForeground": "activeFg", "selectionBackground": "selBg"}, "MainToolbar": {"background": "bg", "Button": {"buttonInsets": "0,0,0,0"}, "Dropdown": {"borderInsets": "6,12,6,12", "background": "bg", "foreground": "fg", "hoverBackground": "hl", "pressedBackground": "table"}, "hoverBackground": "hl", "Icon": {"borderInsets": "10,10,10,10", "background": "bg", "hoverBackground": "hl", "pressedBackground": "table"}, "inactiveBackground": "bg", "pressedBackground": "table", "separatorColor": "border"}, "material": {"background": "bg", "branchColor": "fg", "contrast": "cs", "foreground": "fg", "mergeCommits": "button", "primaryColor": "text", "selectionBackground": "selBg", "selectionForeground": "selFg", "tab.backgroundColor": "bg", "tab.borderColor": "accent", "tagColor": "text"}, "MemoryIndicator": {"allocatedBackground": "second", "unusedColor": "second", "usedBackground": "hl", "usedColor": "hl"}, "menu": "bg", "menuText": "fg", "Menu": {"acceleratorForeground": "text", "acceleratorSelectionForeground": "activeFg", "background": "bg", "border": "8,8,8,8", "borderColor": "second", "disabledBackground": "second", "disabledForeground": "dis", "foreground": "fg", "Selection": {"arc": 8, "innerInsets": "8,2,8,2"}, "selectionBackground": "selBg", "selectionForeground": "activeFg", "separatorColor": "border"}, "MenuBar": {"background": "hc", "borderColor": "bg", "disabledBackground": "bg", "disabledForeground": "dis", "foreground": "fg", "highlight": "bg", "selectionBackground": "selBg", "selectionForeground": "activeFg", "shadow": "hc"}, "MenuItem": {"acceleratorForeground": "text", "acceleratorSelectionForeground": "activeFg", "background": "bg", "border": "8,8,8,8", "disabledBackground": "bg", "disabledForeground": "dis", "foreground": "fg", "selectionBackground": "selBg", "selectionForeground": "activeFg"}, "MlModelBinding.Viewer.CodeEditor.background": "bg", "MnemonicIcon": {"background": "hl", "borderColor": "hl", "foreground": "fg"}, "NavBar": {"arrowColor": "fg", "borderColor": "bg", "selectedColor": "accent"}, "NewClass": {"Panel": {"background": "bg"}, "SearchField": {"background": "cs"}, "separatorWidth": 6}, "NewPSD.warning": "accent", "Notification": {"arc": 16, "Error.foreground": "accent", "Link.foreground": "accent", "background": "notif", "borderColor": "notif", "errorBackground": "notif", "errorBorderColor": "notif", "errorForeground": "accent", "foreground": "fg", "linkForeground": "accent", "MoreButton": {"background": "button", "foreground": "fg", "innerBorderColor": "button"}, "ToolWindow": {"Button.DragAndDrop": {"buttonFloatingBackground": "button", "stripeBackground": "bg"}, "errorBackground": "notifError", "errorBorderColor": "notifError", "errorForeground": "fg", "infoBackground": "notif", "infoBorderColor": "notif", "infoForeground": "fg", "informativeBackground": "notif", "informativeBorderColor": "notif", "informativeForeground": "fg", "warningBackground": "notif<PERSON>arn", "warningBorderColor": "notif<PERSON>arn", "warningForeground": "fg"}, "ToolWindowError": {"foreground": "fg", "background": "notifError"}, "ToolWindowInfo": {"foreground": "fg", "background": "notif", "borderColor": "notif"}, "ToolWindowWarning": {"foreground": "fg", "background": "notif<PERSON>arn"}}, "Notifications": {"background": "notif", "borderColor": "notif"}, "NotificationsToolwindow": {"Notification.hoverBackground": "tree", "newNotification.background": "notif", "newNotification.hoverBackground": "tree"}, "OnePixelDivider.background": "border", "OptionPane": {"background": "bg", "foreground": "fg", "messageForeground": "fg"}, "OptionButton": {"default.separatorColor": "table", "separatorColor": "button"}, "Outline": {"color": "button", "disabledColor": "dis", "focusedColor": "accent"}, "Panel": {"background": "bg", "foreground": "fg", "mouseShortcutBackground": "bg"}, "ParameterInfo": {"ContextHelp.foreground": "text", "background": "second", "borderColor": "table", "currentOverloadBackground": "hl", "currentParameterForeground": "accent", "disabledColor": "dis", "disabledForeground": "dis", "foreground": "fg", "highlightedColor": "accent", "infoForeground": "text", "lineSeparatorColor": "table"}, "PasswordField": {"background": "cs", "capsLockIconColor": "accent", "caretForeground": "accent", "foreground": "fg", "inactiveForeground": "dis", "selectionBackground": "table", "selectionForeground": "activeFg"}, "Plugins": {"background": "bg", "borderColor": "border", "disabledForeground": "dis", "eapTagBackground": "hl", "hoverBackground": "hl", "lightSelectionBackground": "table", "paidTagBackground": "hl", "selectionBackground": "selBg", "selectionForeground": "selFg", "tagBackground": "hl", "tagForeground": "accent", "trialTagBackground": "hl", "Button": {"installBackground": "button", "installBorderColor": "button", "installFillBackground": "button", "installFillForeground": "dis", "installFocusedBackground": "hl", "installForeground": "fg", "updateBackground": "accent", "updateBorderColor": "button", "updateForeground": "fg"}, "ScreenshotPagination": {"CurrentImage.fillColor": "accent"}, "SearchField": {"background": "cs", "borderColor": "border"}, "SectionHeader": {"background": "second", "foreground": "fg"}, "Tab": {"active.background": "table", "active.foreground": "activeFg", "hover.background": "table", "hoverBackground": "table", "selectedBackground": "table", "selectedForeground": "selFg"}}, "Popup": {"Advertiser": {"borderInsets": "6,20,6,20", "background": "bg", "borderColor": "bg", "foreground": "accent"}, "Body": {"bottomInsetNoAd": 8, "bottomInsetBeforeAd": 8, "topInsetNoHeader": 8}, "Border": {"color": "cs", "inactiveColor": "bg"}, "background": "notif", "borderColor": "cs", "borderWidth": 0, "inactiveBorderColor": "bg", "innerBorderColor": "second", "Header": {"activeBackground": "bg", "inactiveBackground": "cs", "inactiveForeground": "text"}, "paintBorder": false, "preferences": {"background": "bg", "borderColor": "bg", "foreground": "fg"}, "Selection.arc": 8, "Selection.leftRightInset": 8, "Separator": {"foreground": "fg", "color": "border"}, "separatorColor": "second", "separatorForeground": "fg", "Toolbar": {"Border.color": "cs", "Floating.background": "cs", "background": "cs", "borderColor": "cs"}}, "PopupMenu": {"background": "bg", "borderWidth": 1, "border": "8,8,8,8", "borderCornerRadius": 8, "foreground": "fg", "Selection": {"arc": 8, "innerInsets": "8,2,8,2"}, "selectionBackground": "selBg", "translucentBackground": "bg"}, "PopupMenuSeparator": {"height": 10, "stripeWidth": 2, "stripeIndent": 5, "withToEdge": 4, "borderCornerRadius": 8}, "ProgressBar": {"background": "bg", "foreground": "accent", "halfColor": "hl", "indeterminateEndColor": "accent", "indeterminateStartColor": "accent", "progressColor": "accent", "selectionBackground": "hl", "trackColor": "hl"}, "ProgressIcon": {"color": "accent"}, "PsiViewer": {"referenceHighlightColor": "accent"}, "RadioButton": {"background": "bg", "darcula": {"borderColor1": "fg", "selectionDisabledColor": "bg", "selectionDisabledShadowColor": "accent", "selectionEnabledColor": "accent", "selectionEnabledShadowColor": "accent"}, "disabledText": "dis", "focusColor": "accent", "foreground": "fg", "selectionDisabledColor": "bg", "selectionDisabledShadowColor": "accent", "selectionEnabledColor": "accent", "selectionEnabledShadowColor": "accent"}, "RadioButtonMenuItem": {"acceleratorForeground": "text", "acceleratorSelectionForeground": "text", "background": "bg", "disabledBackground": "bg", "disabledForeground": "dis", "foreground": "fg", "selectionBackground": "selBg", "selectionForeground": "selFg"}, "ReviewList": {"state.background": "second", "state.foreground": "fg"}, "RunWidget": {"background": "button", "Debug.activeBackground": "hl", "foreground": "fg", "hoverBackground": "table", "pressedBackground": "hl", "Profile.activeBackground": "hl", "runningBackground": "hl", "runningForeground": "selFg", "Run.activeBackground": "hl", "Running": {"background": "hl", "foreground": "selFg", "leftHoverBackground": "hl", "leftPressedBackground": "hl"}, "separatorColor": "border"}, "ScreenView.borderColor": "border", "scrollbar": "bg", "ScrollBar": {"background": "bg", "hoverThumbBorderColor": "accent", "hoverThumbColor": "accent", "hoverTrackColor": "bg30", "Mac": {"hoverThumbBorderColor": "accent", "hoverThumbColor": "accent", "hoverTrackColor": "bg30", "thumbBorderColor": "accent70", "thumbColor": "accent70", "trackColor": "bg30", "Transparent": {"hoverThumbBorderColor": "accent", "hoverThumbColor": "accent", "hoverTrackColor": "bg30", "thumbBorderColor": "accent70", "thumbColor": "accent70", "trackColor": "bg30"}}, "thumb": "hl", "thumbBorderColor": "accent70", "thumbColor": "accent70", "Thumb": {"Hovered.background": "accent", "NonOpaque.Hovered.background": "accent"}, "trackColor": "bg30", "Transparent": {"hoverThumbBorderColor": "accent", "hoverThumbColor": "accent", "hoverTrackColor": "bg30", "thumbBorderColor": "accent70", "thumbColor": "accent70", "trackColor": "bg30"}}, "SearchEverywhere": {"Advertiser": {"background": "bg", "foreground": "text", "borderInsets": "10,20,10,20"}, "background": "bg", "Dialog": {"background": "bg", "borderColor": "bg", "foreground": "fg"}, "foreground": "fg", "Header": {"background": "bg"}, "List": {"Separator.Color": "border", "Separator.foreground": "text", "selectionBackground": "selBg", "separatorColor": "border", "separatorForeground": "text", "settingsBackground": "bg"}, "SearchField": {"Border.color": "hl", "background": "cs", "borderColor": "hl", "grayForeground": "dis", "infoForeground": "dis"}, "shortcutForeground": "text", "Tab": {"active.background": "hl", "active.foreground": "activeFg", "selected.background": "hl", "selected.foreground": "activeFg", "selectedBackground": "hl", "selectedForeground": "activeFg"}}, "SearchMatch": {"endBackground": "accent", "endColor": "accent", "startBackground": "accent", "startColor": "accent"}, "SearchField.errorBackground": "notif", "SearchFieldWithExtension": {"background": "cs"}, "SearchOption": {"selectedBackground": "table", "selectedPressedBackground": "hl", "selectedHoveredBackground": "hl"}, "SearchResults": {"Ordinal.File.Foreground": "text", "Repeated.File.Foreground": "fg"}, "SegmentedButton": {"focusedSelectedButtonColor": "hl", "selectedButtonColor": "button", "selectedStartBorderColor": "border", "selectedEndBorderColor": "border"}, "Separator": {"background": "second", "foreground": "second", "separatorColor": "border"}, "Settings": {"Spotlight.borderColor": "accent"}, "SidePanel": {"background": "hc"}, "Slider": {"background": "bg", "buttonBorderColor": "accent", "buttonColor": "accent", "foreground": "fg", "majorTickLength": 6, "thumb": "accent", "tickColor": "second", "track": "table", "trackColor": "table", "trackDisabled": "hl", "trackWidth": 7}, "Space.Review.diffAnchorBackground": "second", "SpeedSearch": {"background": "hl", "borderColor": "border", "errorForeground": "red", "foreground": "fg"}, "Spinner": {"background": "bg", "border": "4,4,4,4", "disabledBackground": "excl", "foreground": "fg", "selectionForeground": "activeFg"}, "SplitPane": {"background": "bg", "highlight": "hc"}, "SplitPaneDivider.draggingColor": "second", "StateWidget.activeBackground": "button", "StatusBar": {"background": "bg", "Breadcrumbs": {"floatingBackground": "tree", "floatingForeground": "fg", "floatingToolbarInsets": "8,12,8,12", "foreground": "fg", "hoverBackground": "tree", "hoverForeground": "fg", "itemBackgroundInsets": "2,4,2,4", "selectionBackground": "selBg", "selectionForeground": "activeFg", "selectionInactiveBackground": "button", "selectionInactiveForeground": "fg"}, "borderColor": "border", "bottomColor": "bg", "hoverBackground": "hl", "top2Color": "bg", "topColor": "bg", "LightEditBackground": "table", "Widget": {"foreground": "fg", "hoverBackground": "hl", "hoverForeground": "selFg", "pressedBackground": "button", "pressedForeground": "fg", "widgetInsets": "8,12,8,12"}}, "TabbedPane": {"background": "bg", "borderColor": "hc", "contentAreaColor": "hl", "contentBorderInsets": "3,1,1,1", "darkShadow": "hc", "disabledForeground": "dis", "disabledText": "dis", "disabledUnderlineColor": "dis", "focus": "table", "focusColor": "table", "fontSizeOffset": 0, "foreground": "fg", "highlight": "border", "hoverColor": "hl", "labelShift": 0, "mt.tab.background": "hc", "selectHighlight": "hl", "selected": "selBg", "selectedColor": "accent", "selectedDisabledColor": "dis", "selectedForeground": "activeFg", "selectedLabelShift": 0, "selectedTabPadInsets": "0,0,0,0", "shadow": "hc", "tabHeight": 32, "tabInsets": "5,10,5,10", "tabSelectionArc": 4, "tabSelectionHeight": 2, "tabsOverlapBorder": true, "underlineColor": "accent"}, "TabbedPane.mt.tab.background": "hc", "Table": {"alternativeRowBackground": "cs", "background": "bg", "cellNoFocusBorder": "16,4,16,4", "disabledForeground": "dis", "dropLineColor": "accent", "dropLineShortColor": "accent", "focusCellBackground": "table", "focusCellForeground": "activeFg", "focusCellHighlightBorder": "18,6,18,6", "foreground": "fg", "gridColor": "bg", "highlightOuter": "table", "hoverBackground": "hl", "hoverInactiveBackground": "table", "lightSelectionBackground": "table", "lightSelectionForeground": "activeFg", "lightSelectionInactiveBackground": "second", "lightSelectionInactiveForeground": "text", "rowHeight": 32, "selectionBackground": "table", "selectionForeground": "activeFg", "selectionInactiveBackground": "table", "selectionInactiveForeground": "selFg", "sortIconColor": "fg", "stripeColor": "cs", "stripedBackground": "cs"}, "TableHeader": {"background": "button", "bottomSeparatorColor": "border", "borderColor": "hl", "cellBorder": "6,0,6,0", "disabledForeground": "dis", "focusCellBackground": "table", "focusCellForeground": "activeFg", "foreground": "fg", "height": 32, "hoverBackground": "hl", "separatorColor": "border"}, "Tag": {"background": "button", "borderColor": "button", "foreground": "fg"}, "text": "bg", "textInactiveText": "text", "textHighlight": "selBg", "textHighlightText": "activeFg", "textText": "text", "TextArea": {"background": "cs", "caretForeground": "accent", "foreground": "fg", "inactiveForeground": "dis", "selectionBackground": "table", "selectionForeground": "activeFg"}, "TextField": {"background": "cs", "borderColor": "bg", "caretForeground": "accent", "disabledBackground": "excl", "focusedBorderColor": "bg", "foreground": "fg", "hoverBorderColor": "bg", "inactiveForeground": "dis", "selectedSeparatorColor": "accent", "selectionBackground": "selBg", "selectionForeground": "activeFg", "separatorColor": "hl", "separatorColorDisabled": "bg"}, "TextPane": {"background": "cs", "caretForeground": "accent", "foreground": "fg", "inactiveForeground": "dis", "selectionBackground": "table", "selectionForeground": "activeFg"}, "TipOfTheDay": {"Image.borderColor": "border"}, "TitlePane": {"Button": {"hoverBackground": "hl", "preferredSize": "48,40"}, "background": "hc", "inactiveBackground": "bg", "inactiveInfoForeground": "dis", "infoForeground": "text"}, "TitledBorder.titleColor": "fg", "ToggleButton": {"borderColor": "button", "buttonColor": "button", "disabledText": "dis", "foreground": "fg", "off.background": "bg", "off.foreground": "bg", "offBackground": "bg", "offForeground": "bg", "on.background": "accent", "on.foreground": "accent", "onBackground": "accent", "onForeground": "accent"}, "Toolbar.Floating.background": "button", "ToolBar": {"background": "hc", "borderHandleColor": "text", "comboBoxButtonBackground": "button", "floatingForeground": "text", "foreground": "fg", "verticalToolbarInsets": "6,6,6,6", "horizontalToolbarInsets": "6,6,6,6"}, "ToolbarComboWidget": {"background": "button", "hoverBackground": "hl"}, "tooltips": {"actions.keymap.text.color": "text", "actions.settings.icon.background.color": "bg", "description.title.text.color": "fg"}, "ToolTip": {"arc": 6, "actions": {"background": "bg"}, "Actions": {"background": "bg", "grayForeground": "text", "infoForeground": "text"}, "background": "notif", "borderColor": "border", "borderCornerRadius": 8, "foreground": "fg", "infoForeground": "text", "linkForeground": "accent", "Learning": {"background": "accent", "borderColor": "accent", "foreground": "fg", "spanBackground": "accent50", "spanForeground": "fg", "stepNumberForeground": "accent"}, "separatorColor": "border", "shortcutForeground": "text"}, "ToolWindow": {"active": {"Header.background": "second", "HeaderTab.background": "cs"}, "Background": "bg", "background": "hc", "Button": {"hoverBackground": "table", "DragAndDrop": {"stripeBackground": "cs", "buttonDropBackground": "accent70"}, "selectedBackground": "cs", "selectedForeground": "activeFg"}, "header": {"active.background": "second", "background": "bg", "border.background": "second", "closeButton.background": "bg", "tab.selected.active.background": "cs", "tab.selected.background": "cs"}, "Header": {"height": 42, "background": "second", "borderColor": "second", "inactiveBackground": "bg"}, "HeaderCloseButton": {"background": "bg"}, "HeaderTab": {"borderColor": "bg", "hoverBackground": "hl", "hoverInactiveBackground": "hl", "inactiveUnderlineColor": "accent", "leftRightInsets": "0,12,0,12", "selectedBackground": "cs", "selectedInactiveBackground": "cs", "underlineArc": 4, "underlineColor": "accent", "underlineHeight": 3, "underlinedTabBackground": "table", "underlinedTabForeground": "selFg", "underlinedTabInactiveBackground": "hc", "underlinedTabInactiveForeground": "fg", "verticalPadding": 6}, "inactive": {"Header.background": "bg", "HeaderTab.background": "cs"}}, "Tree": {"border": "4,12,4,12", "background": "hc", "foreground": "text", "errorForeground": "red", "hash": "border", "hoverBackground": "tree", "hoverInactiveBackground": "table", "modifiedItemForeground": "accent", "rowHeight": "28", "selectionBackground": "tree", "selectionForeground": "selFg", "selectionInactiveBackground": "tree", "selectionInactiveForeground": "selFg", "textBackground": "hc"}, "Tree.leftChildIndent": 10, "Tree.rightChildIndent": 5, "UIDesigner": {"Activity.borderColor": "border", "Canvas.background": "cs", "ColorPicker": {"background": "second", "foreground": "fg"}, "Component": {"background": "bg", "borderColor": "border", "foreground": "fg", "hoverBorderColor": "hl"}, "Connector": {"borderColor": "border", "hoverBorderColor": "hl"}, "Label.foreground": "text", "highStroke.foreground": "fg", "motion": {"AddConstraintColor": "accent", "AddConstraintPlus": "accent", "CSPanel.SelectedBackground": "tree", "CSPanel.SelectedFocusBackground": "selBg", "Component.foreground": "fg", "ConstraintSet.background": "second", "ConstraintSetText.foreground": "text", "CursorTextColor.foreground": "fg", "HoverColor.disabledBackground": "dis", "Key.selectedForeground": "accent", "Notification.background": "notif", "PositionMarkColor": "accent", "PrimaryPanel.background": "cs", "SecondaryPanel.background": "bg", "SecondaryPanel.header.background": "cs", "SecondaryPanel.header.foreground": "text", "TimeCursor.End.selectedForeground": "accent", "TimeCursor.Start.selectedForeground": "accent", "TimeCursor.selectedForeground": "accent", "borderColor": "border", "cs_FocusText.infoForeground": "text", "graphLine.lineSeparatorColor": "accent", "motionGraph.background": "bg", "ourAvg.background": "second", "ourCS.background": "second", "ourCS_Border.borderColor": "border", "ourCS_SelectedBackground.selectionInactiveBackground": "table", "ourCS_SelectedBorder.pressedBorderColor": "hl", "ourCS_SelectedFocusBackground.selectionForeground": "selFg", "ourCS_SelectedFocusBorder.focusedBorderColor": "accent", "ourCS_TextColor.foreground": "text", "ourML_BarColor.separatorColor": "border", "timeLine.disabledBorderColor": "border"}, "PackageDetails": {"border": "accent", "infoBanner": "accent"}, "PackageSearch": {"PackagesList.rowHeight": 28, "PackageTag": {"background": "excl", "foreground": "fg", "hoverBackground": "tree", "selectedBackground": "selBg", "selectedForeground": "selFg"}, "PackageTagSelected": {"background": "selBg", "foreground": "selFg"}, "SearchResult": {"background": "cs", "hoverBackground": "tree", "PackageTag": {"background": "excl", "foreground": "fg", "hoverBackground": "tree", "selectedBackground": "selBg", "selectedForeground": "selFg"}}}, "Panel": {"background": "bg", "borderColor": "border", "graphLabel": "text", "graphLines": "hl", "lines3d": "accent", "secondaryGraphLines": "border"}, "percent.foreground": "fg", "Placeholder": {"background": "bg", "borderColor": "border", "foreground": "fg", "selectedForeground": "selFg"}, "Preview.background": "bg", "stroke.acceleratorForeground": "text"}, "ValidationTooltip": {"errorBackground": "notif", "errorBorderColor": "notif", "warningBackground": "notif", "warningBorderColor": "notif"}, "VersionControl": {"FileHistory.Commit": {"otherBranchBackground": "excl", "selectedBranchBackground": "bg"}, "GitCommits": {"graphColor": "hl"}, "GitLog": {"localBranchIconColor": "accent", "otherIconColor": "text", "remoteBranchIconColor": "fg", "tagIconColor": "text"}, "HgLog": {"bookmarkIconColor": "activeFg", "branchIconColor": "accent", "closedBranchIconColor": "dis", "localTagIconColor": "text", "mqTagIconColor": "text", "tagIconColor": "text", "tipIconColor": "text"}, "Log": {"Commit": {"currentBranchBackground": "cs", "hoveredBackground": "tree", "rowHeight": 28, "unmatchedForeground": "text"}}, "MarkerPopup": {"borderColor": "border", "borderInsets": "6,8,6,10", "Toolbar.background": "hl"}, "Ref": {"backgroundBase": "hl", "foreground": "activeFg"}, "RefLabel": {"backgroundBase": "hl", "foreground": "activeFg"}}, "UnattendedHostStatus": {"warningBackground": "yellow", "warningForeground": "activeFg", "dangerBackground": "red"}, "Viewport": {"background": "hc", "foreground": "fg"}, "WelcomeScreen": {"AssociatedComponent.background": "bg", "Details.background": "bg", "List": {"background": "hc", "selectionBackground": "selBg", "selectionInactiveBackground": "hl"}, "SidePanel.background": "second", "background": "bg", "borderColor": "bg", "captionBackground": "cs", "captionForeground": "fg", "footerBackground": "cs", "footerForeground": "fg", "groupIconBorderColor": "button", "headerBackground": "bg", "headerForeground": "fg", "separatorColor": "border", "Projects": {"actions.background": "cs", "actions.selectionBackground": "hl", "background": "second", "selectionBackground": "selBg", "selectionInactiveBackground": "selBg"}}, "Window.border": "border", "window": "hc", "windowBorder": "border", "windowText": "text"}, "icons": {"ColorPalette": {"#43494A": "cs", "#6B6B6B": "text", "#A7A7A7": "bg", "#3D6185": "accent", "#466D94": "accent", "#3C3F41": "bg", "#545556": "dis", "#606060": "dis", "#9AA7B0": "fg", "#675133": "accent", "Actions.Blue": "blue", "Actions.Green": "green", "Actions.Grey": "text", "Actions.GreyInline": "gray", "Actions.GreyInline.Dark": "fg", "Actions.Red": "red", "Actions.Yellow": "yellow", "Checkbox.Background.Default": "cs", "Checkbox.Background.Default.Dark": "cs", "Checkbox.Background.Disabled": "excl", "Checkbox.Background.Disabled.Dark": "excl", "Checkbox.Background.Selected": "accent", "Checkbox.Background.Selected.Dark": "hc", "Checkbox.Border.Default": "hl", "Checkbox.Border.Default.Dark": "hl", "Checkbox.Border.Disabled": "dis", "Checkbox.Border.Disabled.Dark": "dis", "Checkbox.Border.Selected": "accent", "Checkbox.Border.Selected.Dark": "hl", "Checkbox.Focus.Thin.Default": "accent", "Checkbox.Focus.Thin.Default.Dark": "accent", "Checkbox.Focus.Thin.Selected": "fg", "Checkbox.Focus.Thin.Selected.Dark": "fg", "Checkbox.Focus.Wide": "accent", "Checkbox.Focus.Wide.Dark": "accent", "Checkbox.Foreground.Disabled": "dis", "Checkbox.Foreground.Disabled.Dark": "dis", "Checkbox.Foreground.Selected": "hc", "Checkbox.Foreground.Selected.Dark": "accent", "Objects.BlackText": "fg", "Objects.Blue": "blue", "Objects.Green": "green", "Objects.GreenAndroid": "green", "Objects.Grey": "gray", "Objects.Pink": "purple", "Objects.Purple": "purple", "Objects.Red": "red", "Objects.RedStatus": "red", "Objects.Yellow": "yellow", "Objects.YellowDark": "orange", "Tree.iconColor": "text", "Tree.iconColor.Dark": "text"}}}